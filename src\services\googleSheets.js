const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

class GoogleSheetsService {
  constructor() {
    this.sheets = null;
    this.auth = null;
    this.spreadsheetId = process.env.GOOGLE_SHEETS_ID;
    this.serviceAccountKeyPath = process.env.GOOGLE_SERVICE_ACCOUNT_KEY_PATH;
  }

  async initialize() {
    try {
      // Check if service account key file exists
      if (!fs.existsSync(this.serviceAccountKeyPath)) {
        throw new Error(`Google service account key file not found at: ${this.serviceAccountKeyPath}`);
      }

      // Load service account credentials
      const credentials = JSON.parse(fs.readFileSync(this.serviceAccountKeyPath, 'utf8'));

      // Create JWT auth client
      this.auth = new google.auth.JWT(
        credentials.client_email,
        null,
        credentials.private_key,
        ['https://www.googleapis.com/auth/spreadsheets.readonly']
      );

      // Initialize Sheets API
      this.sheets = google.sheets({ version: 'v4', auth: this.auth });

      logger.info('Google Sheets service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Google Sheets service:', error);
      throw error;
    }
  }

  async getSheetData(range = 'A:Z') {
    try {
      if (!this.sheets) {
        await this.initialize();
      }

      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: this.spreadsheetId,
        range: range
      });

      return response.data.values || [];
    } catch (error) {
      logger.error('Failed to get sheet data:', error);
      throw error;
    }
  }

  async getURLsFromSheet(sheetName = 'Sheet1', urlColumnName = 'URL') {
    try {
      // Get all data from the sheet
      const range = sheetName ? `${sheetName}!A:Z` : 'A:Z';
      const data = await this.getSheetData(range);

      if (data.length === 0) {
        logger.warn('No data found in Google Sheet');
        return [];
      }

      // Find the header row (assumed to be the first row)
      const headers = data[0];
      const urlColumnIndex = headers.findIndex(header => 
        header && header.toLowerCase().trim() === urlColumnName.toLowerCase().trim()
      );

      if (urlColumnIndex === -1) {
        throw new Error(`Column "${urlColumnName}" not found in sheet. Available columns: ${headers.join(', ')}`);
      }

      // Extract URLs from the specified column (skip header row)
      const urls = [];
      for (let i = 1; i < data.length; i++) {
        const row = data[i];
        if (row && row[urlColumnIndex]) {
          const url = row[urlColumnIndex].trim();
          if (this.isValidURL(url)) {
            urls.push({
              url: url,
              row: i + 1, // 1-based row number
              rawData: row
            });
          } else {
            logger.warn(`Invalid URL found in row ${i + 1}: ${url}`);
          }
        }
      }

      logger.info(`Found ${urls.length} valid URLs in Google Sheet`);
      return urls;

    } catch (error) {
      logger.error('Failed to get URLs from sheet:', error);
      throw error;
    }
  }

  async getNewURLs(lastProcessedRow = 0) {
    try {
      const allUrls = await this.getURLsFromSheet();
      
      // Filter URLs that haven't been processed yet
      const newUrls = allUrls.filter(item => item.row > lastProcessedRow);
      
      logger.info(`Found ${newUrls.length} new URLs since row ${lastProcessedRow}`);
      return newUrls;

    } catch (error) {
      logger.error('Failed to get new URLs:', error);
      throw error;
    }
  }

  async getSheetInfo() {
    try {
      if (!this.sheets) {
        await this.initialize();
      }

      const response = await this.sheets.spreadsheets.get({
        spreadsheetId: this.spreadsheetId
      });

      const spreadsheet = response.data;
      const sheets = spreadsheet.sheets.map(sheet => ({
        title: sheet.properties.title,
        sheetId: sheet.properties.sheetId,
        rowCount: sheet.properties.gridProperties.rowCount,
        columnCount: sheet.properties.gridProperties.columnCount
      }));

      return {
        title: spreadsheet.properties.title,
        sheets: sheets,
        url: `https://docs.google.com/spreadsheets/d/${this.spreadsheetId}`
      };

    } catch (error) {
      logger.error('Failed to get sheet info:', error);
      throw error;
    }
  }

  async validateSheetAccess() {
    try {
      const info = await this.getSheetInfo();
      logger.info('Sheet access validated successfully:', {
        title: info.title,
        sheetsCount: info.sheets.length
      });
      return true;
    } catch (error) {
      logger.error('Sheet access validation failed:', error);
      return false;
    }
  }

  isValidURL(string) {
    try {
      const url = new URL(string);
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (_) {
      return false;
    }
  }

  // Method to get sample data for testing
  async getSampleData(maxRows = 5) {
    try {
      const data = await this.getSheetData('A1:Z' + (maxRows + 1)); // +1 for header
      return data;
    } catch (error) {
      logger.error('Failed to get sample data:', error);
      throw error;
    }
  }

  // Method to detect column structure
  async detectColumns() {
    try {
      const data = await this.getSheetData('A1:Z1'); // Get first row only
      if (data.length > 0) {
        const headers = data[0];
        const columnInfo = headers.map((header, index) => ({
          index: index,
          name: header,
          letter: String.fromCharCode(65 + index) // A, B, C, etc.
        }));

        // Try to identify URL column
        const urlColumn = columnInfo.find(col => 
          col.name && col.name.toLowerCase().includes('url')
        );

        return {
          headers: columnInfo,
          suggestedUrlColumn: urlColumn ? urlColumn.name : null,
          totalColumns: headers.length
        };
      }
      return null;
    } catch (error) {
      logger.error('Failed to detect columns:', error);
      throw error;
    }
  }
}

module.exports = new GoogleSheetsService();
