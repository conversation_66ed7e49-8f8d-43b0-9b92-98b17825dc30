<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automated Blogging System - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 10px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .controls {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .controls h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .jobs-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .jobs-section h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .jobs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .jobs-table th,
        .jobs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .jobs-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }

        .url-input {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .url-input input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .refresh-indicator {
            display: inline-block;
            margin-left: 10px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .refresh-indicator.active {
            opacity: 1;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .spinner {
            animation: spin 1s linear infinite;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #eee;
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 600;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .button-group {
                flex-direction: column;
            }

            .url-input {
                flex-direction: column;
            }

            .jobs-table {
                font-size: 14px;
            }

            .jobs-table th,
            .jobs-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Automated Blogging System</h1>
            <p>Monitor and manage your automated blog posting pipeline</p>
        </div>

        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="totalJobs">-</div>
                <div class="stat-label">Total Jobs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingJobs">-</div>
                <div class="stat-label">Pending</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="completedJobs">-</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedJobs">-</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="queueSize">-</div>
                <div class="stat-label">Queue Size</div>
            </div>
        </div>

        <div class="controls">
            <h2>System Controls <span class="refresh-indicator" id="refreshIndicator">🔄</span></h2>
            <div class="button-group">
                <button class="btn btn-primary" onclick="triggerProcessing()">▶️ Trigger Processing</button>
                <button class="btn btn-secondary" onclick="triggerURLFetch()">📥 Fetch URLs</button>
                <button class="btn btn-success" onclick="testConnections()">🔧 Test Connections</button>
                <button class="btn btn-warning" onclick="refreshData()">🔄 Refresh Data</button>
            </div>
            <div id="controlMessages"></div>
        </div>

        <div class="jobs-section">
            <h2>Add New URL</h2>
            <div class="url-input">
                <input type="url" id="newUrlInput" placeholder="Enter URL to add to queue..." />
                <button class="btn btn-primary" onclick="addURL()">Add URL</button>
            </div>
        </div>

        <div class="jobs-section">
            <div class="tabs">
                <div class="tab active" onclick="switchTab('recent')">Recent Jobs</div>
                <div class="tab" onclick="switchTab('all')">All Jobs</div>
                <div class="tab" onclick="switchTab('failed')">Failed Jobs</div>
            </div>

            <div class="tab-content active" id="recent-content">
                <h3>Recent Jobs</h3>
                <div id="recentJobs" class="loading">Loading recent jobs...</div>
            </div>

            <div class="tab-content" id="all-content">
                <h3>All Jobs</h3>
                <div id="allJobs" class="loading">Loading all jobs...</div>
            </div>

            <div class="tab-content" id="failed-content">
                <h3>Failed Jobs</h3>
                <div id="failedJobs" class="loading">Loading failed jobs...</div>
            </div>
        </div>
    </div>

    <script>
        let refreshInterval;
        let currentTab = 'recent';

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            startAutoRefresh();
        });

        function startAutoRefresh() {
            refreshInterval = setInterval(refreshData, 30000); // Refresh every 30 seconds
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        async function refreshData() {
            const indicator = document.getElementById('refreshIndicator');
            indicator.classList.add('active', 'spinner');

            try {
                await Promise.all([
                    loadSystemStatus(),
                    loadRecentJobs()
                ]);
            } catch (error) {
                console.error('Refresh error:', error);
            } finally {
                indicator.classList.remove('active', 'spinner');
            }
        }

        async function loadSystemStatus() {
            try {
                const response = await fetch('/dashboard/api/status');
                const data = await response.json();

                if (response.ok) {
                    updateStats(data.database);
                    document.getElementById('queueSize').textContent = data.queueSize || 0;
                } else {
                    throw new Error(data.error || 'Failed to load status');
                }
            } catch (error) {
                console.error('Status load error:', error);
                showMessage('error', 'Failed to load system status: ' + error.message);
            }
        }

        function updateStats(stats) {
            document.getElementById('totalJobs').textContent = stats.total || 0;
            document.getElementById('pendingJobs').textContent = stats.pending || 0;
            document.getElementById('completedJobs').textContent = stats.completed || 0;
            document.getElementById('failedJobs').textContent = stats.failed || 0;
        }

        async function loadRecentJobs() {
            try {
                const response = await fetch('/dashboard/api/jobs?limit=10');
                const data = await response.json();

                if (response.ok) {
                    displayJobs(data.jobs, 'recentJobs');
                } else {
                    throw new Error(data.error || 'Failed to load jobs');
                }
            } catch (error) {
                console.error('Jobs load error:', error);
                document.getElementById('recentJobs').innerHTML = 
                    '<div class="error">Failed to load jobs: ' + error.message + '</div>';
            }
        }

        function displayJobs(jobs, containerId) {
            const container = document.getElementById(containerId);
            
            if (jobs.length === 0) {
                container.innerHTML = '<p>No jobs found.</p>';
                return;
            }

            const table = `
                <table class="jobs-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>URL</th>
                            <th>Status</th>
                            <th>Title</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${jobs.map(job => `
                            <tr>
                                <td>${job.id}</td>
                                <td><a href="${job.url}" target="_blank" title="${job.url}">
                                    ${job.url.length > 50 ? job.url.substring(0, 50) + '...' : job.url}
                                </a></td>
                                <td><span class="status-badge status-${job.status}">${job.status}</span></td>
                                <td>${job.title || 'N/A'}</td>
                                <td>${new Date(job.created_at).toLocaleDateString()}</td>
                                <td>
                                    <button class="btn btn-sm" onclick="viewJob(${job.id})">View</button>
                                    ${job.status === 'failed' ? 
                                        `<button class="btn btn-sm btn-warning" onclick="retryJob(${job.id})">Retry</button>` : 
                                        ''
                                    }
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = table;
        }

        async function triggerProcessing() {
            try {
                const response = await fetch('/dashboard/api/trigger/processing', {
                    method: 'POST'
                });
                const data = await response.json();

                if (response.ok) {
                    showMessage('success', 'Processing triggered successfully');
                    setTimeout(refreshData, 2000);
                } else {
                    throw new Error(data.error || 'Failed to trigger processing');
                }
            } catch (error) {
                showMessage('error', 'Failed to trigger processing: ' + error.message);
            }
        }

        async function triggerURLFetch() {
            try {
                const response = await fetch('/dashboard/api/trigger/url-fetch', {
                    method: 'POST'
                });
                const data = await response.json();

                if (response.ok) {
                    showMessage('success', 'URL fetch triggered successfully');
                    setTimeout(refreshData, 2000);
                } else {
                    throw new Error(data.error || 'Failed to trigger URL fetch');
                }
            } catch (error) {
                showMessage('error', 'Failed to trigger URL fetch: ' + error.message);
            }
        }

        async function testConnections() {
            showMessage('info', 'Testing connections...');
            
            try {
                // Test Google Sheets
                const sheetsResponse = await fetch('/dashboard/api/test/google-sheets', {
                    method: 'POST'
                });
                const sheetsData = await sheetsResponse.json();

                // Test WordPress
                const wpResponse = await fetch('/dashboard/api/test/wordpress', {
                    method: 'POST'
                });
                const wpData = await wpResponse.json();

                let message = 'Connection Test Results:\n';
                message += `Google Sheets: ${sheetsData.success ? '✅ Connected' : '❌ Failed'}\n`;
                message += `WordPress: ${wpData.success ? '✅ Connected' : '❌ Failed'}`;

                showMessage(sheetsData.success && wpData.success ? 'success' : 'warning', message);

            } catch (error) {
                showMessage('error', 'Connection test failed: ' + error.message);
            }
        }

        async function addURL() {
            const input = document.getElementById('newUrlInput');
            const url = input.value.trim();

            if (!url) {
                showMessage('error', 'Please enter a URL');
                return;
            }

            try {
                const response = await fetch('/dashboard/api/jobs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ url })
                });
                const data = await response.json();

                if (response.ok) {
                    showMessage('success', 'URL added to queue successfully');
                    input.value = '';
                    setTimeout(refreshData, 1000);
                } else {
                    throw new Error(data.error || 'Failed to add URL');
                }
            } catch (error) {
                showMessage('error', 'Failed to add URL: ' + error.message);
            }
        }

        function switchTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName + '-content').classList.add('active');

            currentTab = tabName;

            // Load appropriate data
            if (tabName === 'all') {
                loadAllJobs();
            } else if (tabName === 'failed') {
                loadFailedJobs();
            }
        }

        async function loadAllJobs() {
            try {
                const response = await fetch('/dashboard/api/jobs?limit=50');
                const data = await response.json();

                if (response.ok) {
                    displayJobs(data.jobs, 'allJobs');
                } else {
                    throw new Error(data.error || 'Failed to load jobs');
                }
            } catch (error) {
                document.getElementById('allJobs').innerHTML = 
                    '<div class="error">Failed to load jobs: ' + error.message + '</div>';
            }
        }

        async function loadFailedJobs() {
            try {
                const response = await fetch('/dashboard/api/jobs?status=failed&limit=50');
                const data = await response.json();

                if (response.ok) {
                    displayJobs(data.jobs, 'failedJobs');
                } else {
                    throw new Error(data.error || 'Failed to load jobs');
                }
            } catch (error) {
                document.getElementById('failedJobs').innerHTML = 
                    '<div class="error">Failed to load jobs: ' + error.message + '</div>';
            }
        }

        async function retryJob(jobId) {
            try {
                const response = await fetch(`/dashboard/api/jobs/${jobId}/retry`, {
                    method: 'POST'
                });
                const data = await response.json();

                if (response.ok) {
                    showMessage('success', 'Job queued for retry');
                    setTimeout(refreshData, 1000);
                } else {
                    throw new Error(data.error || 'Failed to retry job');
                }
            } catch (error) {
                showMessage('error', 'Failed to retry job: ' + error.message);
            }
        }

        function viewJob(jobId) {
            // This could open a modal or navigate to a detailed view
            window.open(`/dashboard/job/${jobId}`, '_blank');
        }

        function showMessage(type, message) {
            const container = document.getElementById('controlMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            
            container.innerHTML = '';
            container.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // Handle page visibility changes to pause/resume auto-refresh
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        });
    </script>
</body>
</html>
