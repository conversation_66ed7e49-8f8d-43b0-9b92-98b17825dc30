const { GoogleGenerativeAI } = require('@google/generative-ai');
const TurndownService = require('turndown');
const slug = require('slug');
const logger = require('../utils/logger');

class ContentProcessorService {
  constructor() {
    this.genAI = null;
    this.model = null;
    this.turndownService = new TurndownService();
    this.apiKey = process.env.GEMINI_API_KEY;
  }

  async initialize() {
    try {
      if (!this.apiKey) {
        throw new Error('GEMINI_API_KEY environment variable is required');
      }

      this.genAI = new GoogleGenerativeAI(this.apiKey);
      this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      logger.info('Content processor initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize content processor:', error);
      throw error;
    }
  }

  async processContent(scrapedContent, options = {}) {
    try {
      if (!this.model) {
        await this.initialize();
      }

      const { title, text, metaDescription, url } = scrapedContent;
      
      // Generate rewritten content
      const rewrittenContent = await this.rewriteContent(text, title);
      
      // Generate SEO-optimized title
      const seoTitle = await this.generateSEOTitle(title, rewrittenContent.content);
      
      // Generate meta description
      const seoMetaDescription = await this.generateMetaDescription(rewrittenContent.content);
      
      // Generate categories and tags
      const categoriesAndTags = await this.generateCategoriesAndTags(rewrittenContent.content);
      
      // Generate slug
      const seoSlug = this.generateSlug(seoTitle);
      
      // Extract or generate featured image suggestions
      const featuredImageSuggestions = await this.generateImageSuggestions(seoTitle, rewrittenContent.content);

      const processedContent = {
        originalTitle: title,
        originalUrl: url,
        title: seoTitle,
        content: rewrittenContent.content,
        excerpt: rewrittenContent.excerpt,
        metaDescription: seoMetaDescription,
        slug: seoSlug,
        categories: categoriesAndTags.categories,
        tags: categoriesAndTags.tags,
        featuredImageSuggestions: featuredImageSuggestions,
        wordCount: rewrittenContent.content.split(/\s+/).length,
        readingTime: Math.ceil(rewrittenContent.content.split(/\s+/).length / 200), // Assuming 200 WPM
        processedAt: new Date().toISOString(),
        seoScore: this.calculateSEOScore(seoTitle, rewrittenContent.content, seoMetaDescription)
      };

      logger.logProcessing(url, 'success', {
        originalWordCount: text.split(/\s+/).length,
        processedWordCount: processedContent.wordCount,
        seoScore: processedContent.seoScore
      });

      return processedContent;

    } catch (error) {
      logger.logProcessing(scrapedContent.url, 'error', { error: error.message });
      throw error;
    }
  }

  async rewriteContent(originalText, originalTitle) {
    const prompt = `
You are an expert content writer specializing in job-related blog posts. Your task is to completely rewrite the following content to make it:

1. 100% plagiarism-free and original
2. Human-like and engaging
3. SEO-optimized with proper structure
4. Professional and informative
5. Focused on job seekers and career development

Original Title: ${originalTitle}
Original Content: ${originalText}

Please rewrite this content following these guidelines:
- Create a compelling introduction that hooks the reader
- Use clear, professional language that's easy to understand
- Structure the content with proper headings (H2, H3) for better readability
- Include actionable advice and practical tips
- Make it relevant for job seekers and career professionals
- Ensure the content is at least 800 words but not more than 1500 words
- Use bullet points and numbered lists where appropriate
- End with a strong conclusion that encourages action

Format your response as:
CONTENT:
[Your rewritten content here with proper HTML formatting including h2, h3, p, ul, ol tags]

EXCERPT:
[A compelling 2-3 sentence excerpt that summarizes the main value proposition]
`;

    try {
      const result = await this.model.generateContent(prompt);
      const response = result.response.text();
      
      // Parse the response to extract content and excerpt
      const contentMatch = response.match(/CONTENT:\s*([\s\S]*?)(?=EXCERPT:|$)/);
      const excerptMatch = response.match(/EXCERPT:\s*([\s\S]*?)$/);
      
      const content = contentMatch ? contentMatch[1].trim() : response;
      const excerpt = excerptMatch ? excerptMatch[1].trim() : this.generateExcerpt(content);

      return {
        content: content,
        excerpt: excerpt
      };
    } catch (error) {
      logger.error('Failed to rewrite content:', error);
      throw error;
    }
  }

  async generateSEOTitle(originalTitle, content) {
    const prompt = `
Based on the following content, create an SEO-optimized title that:
- Is between 50-60 characters
- Includes relevant keywords
- Is compelling and click-worthy
- Appeals to job seekers and career professionals
- Follows best SEO practices

Original Title: ${originalTitle}
Content Preview: ${content.substring(0, 500)}...

Generate 3 title options and mark the best one with [BEST]:
`;

    try {
      const result = await this.model.generateContent(prompt);
      const response = result.response.text();
      
      // Extract the best title
      const bestTitleMatch = response.match(/\[BEST\]:\s*(.+)/);
      if (bestTitleMatch) {
        return bestTitleMatch[1].trim();
      }
      
      // Fallback: extract first title
      const lines = response.split('\n').filter(line => line.trim());
      return lines[0].replace(/^\d+\.\s*/, '').trim();
      
    } catch (error) {
      logger.error('Failed to generate SEO title:', error);
      return originalTitle;
    }
  }

  async generateMetaDescription(content) {
    const prompt = `
Create an SEO-optimized meta description for the following content:
- Must be between 150-160 characters
- Should be compelling and encourage clicks
- Include relevant keywords naturally
- Focus on the value proposition for job seekers

Content: ${content.substring(0, 800)}...

Generate only the meta description:
`;

    try {
      const result = await this.model.generateContent(prompt);
      const metaDescription = result.response.text().trim();
      
      // Ensure it's within character limits
      if (metaDescription.length > 160) {
        return metaDescription.substring(0, 157) + '...';
      }
      
      return metaDescription;
    } catch (error) {
      logger.error('Failed to generate meta description:', error);
      return content.substring(0, 157) + '...';
    }
  }

  async generateCategoriesAndTags(content) {
    const prompt = `
Based on the following job-related content, suggest:
1. 2-3 relevant categories (broad topics)
2. 5-8 specific tags (specific keywords)

Focus on job search, career development, workplace topics, and industry-specific terms.

Content: ${content.substring(0, 1000)}...

Format your response as:
CATEGORIES: category1, category2, category3
TAGS: tag1, tag2, tag3, tag4, tag5, tag6, tag7, tag8
`;

    try {
      const result = await this.model.generateContent(prompt);
      const response = result.response.text();
      
      const categoriesMatch = response.match(/CATEGORIES:\s*(.+)/);
      const tagsMatch = response.match(/TAGS:\s*(.+)/);
      
      const categories = categoriesMatch 
        ? categoriesMatch[1].split(',').map(cat => cat.trim()).filter(cat => cat)
        : ['Career Development', 'Job Search'];
        
      const tags = tagsMatch 
        ? tagsMatch[1].split(',').map(tag => tag.trim()).filter(tag => tag)
        : ['jobs', 'career', 'employment', 'workplace', 'professional development'];

      return {
        categories: categories.slice(0, 3),
        tags: tags.slice(0, 8)
      };
    } catch (error) {
      logger.error('Failed to generate categories and tags:', error);
      return {
        categories: ['Career Development', 'Job Search'],
        tags: ['jobs', 'career', 'employment', 'workplace']
      };
    }
  }

  async generateImageSuggestions(title, content) {
    const prompt = `
Based on this job-related article title and content, suggest 3 relevant featured image concepts:

Title: ${title}
Content Preview: ${content.substring(0, 500)}...

Provide image suggestions that would be:
- Professional and relevant to the content
- Appealing to job seekers and career professionals
- Suitable for stock photo searches
- Visually engaging for social media sharing

Format as:
1. [Image concept 1]
2. [Image concept 2]
3. [Image concept 3]
`;

    try {
      const result = await this.model.generateContent(prompt);
      const response = result.response.text();
      
      const suggestions = response
        .split('\n')
        .filter(line => line.match(/^\d+\./))
        .map(line => line.replace(/^\d+\.\s*/, '').trim())
        .filter(suggestion => suggestion);

      return suggestions.length > 0 ? suggestions : [
        'Professional business meeting',
        'Person working on laptop',
        'Career growth concept'
      ];
    } catch (error) {
      logger.error('Failed to generate image suggestions:', error);
      return [
        'Professional business meeting',
        'Person working on laptop',
        'Career growth concept'
      ];
    }
  }

  generateSlug(title) {
    return slug(title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g
    });
  }

  generateExcerpt(content) {
    // Remove HTML tags and get first 2-3 sentences
    const plainText = content.replace(/<[^>]*>/g, '');
    const sentences = plainText.split(/[.!?]+/).filter(s => s.trim().length > 10);
    
    if (sentences.length >= 2) {
      return sentences.slice(0, 2).join('. ').trim() + '.';
    } else if (sentences.length === 1) {
      return sentences[0].trim() + '.';
    } else {
      return plainText.substring(0, 200).trim() + '...';
    }
  }

  calculateSEOScore(title, content, metaDescription) {
    let score = 0;
    
    // Title length (50-60 chars is optimal)
    if (title.length >= 50 && title.length <= 60) score += 20;
    else if (title.length >= 40 && title.length <= 70) score += 15;
    else score += 5;
    
    // Content length (800-1500 words is good)
    const wordCount = content.split(/\s+/).length;
    if (wordCount >= 800 && wordCount <= 1500) score += 25;
    else if (wordCount >= 500 && wordCount <= 2000) score += 20;
    else score += 10;
    
    // Meta description length (150-160 chars is optimal)
    if (metaDescription.length >= 150 && metaDescription.length <= 160) score += 15;
    else if (metaDescription.length >= 120 && metaDescription.length <= 170) score += 10;
    else score += 5;
    
    // Heading structure
    const headingCount = (content.match(/<h[2-6][^>]*>/gi) || []).length;
    if (headingCount >= 3) score += 20;
    else if (headingCount >= 1) score += 15;
    else score += 5;
    
    // List usage
    const listCount = (content.match(/<(ul|ol)[^>]*>/gi) || []).length;
    if (listCount >= 1) score += 10;
    
    // Keyword density (basic check for job-related terms)
    const jobKeywords = ['job', 'career', 'work', 'employment', 'professional', 'skill'];
    const keywordMatches = jobKeywords.filter(keyword => 
      content.toLowerCase().includes(keyword)
    ).length;
    score += Math.min(keywordMatches * 2, 10);
    
    return Math.min(score, 100);
  }

  // Method to test content processing
  async testProcessing(sampleText, sampleTitle) {
    try {
      const mockScrapedContent = {
        title: sampleTitle || 'Sample Job Article',
        text: sampleText || 'This is a sample article about job searching and career development.',
        metaDescription: '',
        url: 'https://example.com/test'
      };

      const result = await this.processContent(mockScrapedContent);
      return {
        success: true,
        data: result,
        message: 'Content processing test successful'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Content processing test failed'
      };
    }
  }
}

module.exports = new ContentProcessorService();
