<?php
/**
 * Plugin Name: Automated Blogging API
 * Plugin URI: https://github.com/your-repo/automated-blogging-api
 * Description: WordPress plugin that provides secure API endpoints for automated blog post publishing from external systems.
 * Version: 1.0.0
 * Author: Automated Blogging System
 * License: GPL v2 or later
 * Text Domain: automated-blogging-api
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('ABA_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ABA_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('ABA_VERSION', '1.0.0');

class AutomatedBloggingAPI {
    
    private $api_key;
    private $allowed_ips = array();
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('rest_api_init', array($this, 'register_api_routes'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Load plugin options
        $this->api_key = get_option('aba_api_key', '');
        $this->allowed_ips = get_option('aba_allowed_ips', array());
        
        // Generate API key if not exists
        if (empty($this->api_key)) {
            $this->api_key = $this->generate_api_key();
            update_option('aba_api_key', $this->api_key);
        }
    }
    
    public function register_api_routes() {
        // Register API endpoints
        register_rest_route('automated-blogging/v1', '/publish', array(
            'methods' => 'POST',
            'callback' => array($this, 'publish_post'),
            'permission_callback' => array($this, 'verify_api_access'),
            'args' => array(
                'title' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'content' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'wp_kses_post'
                ),
                'excerpt' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_textarea_field'
                ),
                'slug' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_title'
                ),
                'categories' => array(
                    'required' => false,
                    'type' => 'array'
                ),
                'tags' => array(
                    'required' => false,
                    'type' => 'array'
                ),
                'meta_description' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_textarea_field'
                ),
                'featured_image_url' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'esc_url_raw'
                ),
                'status' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'publish',
                    'enum' => array('publish', 'draft', 'pending')
                ),
                'author_id' => array(
                    'required' => false,
                    'type' => 'integer',
                    'default' => 1
                )
            )
        ));
        
        // Health check endpoint
        register_rest_route('automated-blogging/v1', '/health', array(
            'methods' => 'GET',
            'callback' => array($this, 'health_check'),
            'permission_callback' => '__return_true'
        ));
        
        // Get categories endpoint
        register_rest_route('automated-blogging/v1', '/categories', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_categories'),
            'permission_callback' => array($this, 'verify_api_access')
        ));
        
        // Get tags endpoint
        register_rest_route('automated-blogging/v1', '/tags', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_tags'),
            'permission_callback' => array($this, 'verify_api_access')
        ));
    }
    
    public function verify_api_access($request) {
        // Check API key
        $provided_key = $request->get_header('X-API-Key');
        if (empty($provided_key) || $provided_key !== $this->api_key) {
            return new WP_Error('invalid_api_key', 'Invalid API key', array('status' => 401));
        }
        
        // Check IP whitelist if configured
        if (!empty($this->allowed_ips)) {
            $client_ip = $this->get_client_ip();
            if (!in_array($client_ip, $this->allowed_ips)) {
                return new WP_Error('ip_not_allowed', 'IP address not allowed', array('status' => 403));
            }
        }
        
        return true;
    }
    
    public function publish_post($request) {
        try {
            $params = $request->get_params();
            
            // Prepare post data
            $post_data = array(
                'post_title' => $params['title'],
                'post_content' => $params['content'],
                'post_status' => $params['status'],
                'post_author' => $params['author_id'],
                'post_type' => 'post'
            );
            
            // Add excerpt if provided
            if (!empty($params['excerpt'])) {
                $post_data['post_excerpt'] = $params['excerpt'];
            }
            
            // Add slug if provided
            if (!empty($params['slug'])) {
                $post_data['post_name'] = $params['slug'];
            }
            
            // Insert the post
            $post_id = wp_insert_post($post_data);
            
            if (is_wp_error($post_id)) {
                return new WP_Error('post_creation_failed', $post_id->get_error_message(), array('status' => 500));
            }
            
            // Handle categories
            if (!empty($params['categories'])) {
                $category_ids = $this->handle_categories($params['categories']);
                wp_set_post_categories($post_id, $category_ids);
            }
            
            // Handle tags
            if (!empty($params['tags'])) {
                wp_set_post_tags($post_id, $params['tags']);
            }
            
            // Handle meta description
            if (!empty($params['meta_description'])) {
                update_post_meta($post_id, '_yoast_wpseo_metadesc', $params['meta_description']);
                update_post_meta($post_id, 'meta_description', $params['meta_description']);
            }
            
            // Handle featured image
            $featured_image_id = null;
            if (!empty($params['featured_image_url'])) {
                $featured_image_id = $this->handle_featured_image($post_id, $params['featured_image_url']);
            }
            
            // Log the successful creation
            $this->log_activity('post_created', array(
                'post_id' => $post_id,
                'title' => $params['title'],
                'status' => $params['status']
            ));
            
            return rest_ensure_response(array(
                'success' => true,
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'edit_url' => get_edit_post_link($post_id, 'raw'),
                'featured_image_id' => $featured_image_id,
                'message' => 'Post created successfully'
            ));
            
        } catch (Exception $e) {
            $this->log_activity('post_creation_error', array(
                'error' => $e->getMessage(),
                'title' => $params['title'] ?? 'Unknown'
            ));
            
            return new WP_Error('post_creation_exception', $e->getMessage(), array('status' => 500));
        }
    }
    
    private function handle_categories($categories) {
        $category_ids = array();
        
        foreach ($categories as $category_name) {
            $category = get_term_by('name', $category_name, 'category');
            
            if (!$category) {
                // Create category if it doesn't exist
                $result = wp_insert_term($category_name, 'category');
                if (!is_wp_error($result)) {
                    $category_ids[] = $result['term_id'];
                }
            } else {
                $category_ids[] = $category->term_id;
            }
        }
        
        return $category_ids;
    }
    
    private function handle_featured_image($post_id, $image_url) {
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        
        try {
            // Download image
            $tmp = download_url($image_url);
            
            if (is_wp_error($tmp)) {
                return null;
            }
            
            // Prepare file array
            $file_array = array(
                'name' => basename($image_url),
                'tmp_name' => $tmp
            );
            
            // Upload image to media library
            $attachment_id = media_handle_sideload($file_array, $post_id);
            
            // Clean up temp file
            @unlink($tmp);
            
            if (is_wp_error($attachment_id)) {
                return null;
            }
            
            // Set as featured image
            set_post_thumbnail($post_id, $attachment_id);
            
            return $attachment_id;
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    public function health_check($request) {
        return rest_ensure_response(array(
            'status' => 'healthy',
            'plugin_version' => ABA_VERSION,
            'wordpress_version' => get_bloginfo('version'),
            'timestamp' => current_time('mysql'),
            'api_endpoints' => array(
                'publish' => rest_url('automated-blogging/v1/publish'),
                'categories' => rest_url('automated-blogging/v1/categories'),
                'tags' => rest_url('automated-blogging/v1/tags')
            )
        ));
    }
    
    public function get_categories($request) {
        $categories = get_categories(array(
            'hide_empty' => false,
            'orderby' => 'name',
            'order' => 'ASC'
        ));
        
        $formatted_categories = array();
        foreach ($categories as $category) {
            $formatted_categories[] = array(
                'id' => $category->term_id,
                'name' => $category->name,
                'slug' => $category->slug,
                'count' => $category->count
            );
        }
        
        return rest_ensure_response($formatted_categories);
    }
    
    public function get_tags($request) {
        $tags = get_tags(array(
            'hide_empty' => false,
            'orderby' => 'name',
            'order' => 'ASC'
        ));
        
        $formatted_tags = array();
        foreach ($tags as $tag) {
            $formatted_tags[] = array(
                'id' => $tag->term_id,
                'name' => $tag->name,
                'slug' => $tag->slug,
                'count' => $tag->count
            );
        }
        
        return rest_ensure_response($formatted_tags);
    }
    
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    private function generate_api_key() {
        return wp_generate_password(32, false);
    }
    
    private function log_activity($action, $data = array()) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'action' => $action,
            'data' => $data,
            'ip' => $this->get_client_ip()
        );
        
        $logs = get_option('aba_activity_logs', array());
        array_unshift($logs, $log_entry);
        
        // Keep only last 100 entries
        $logs = array_slice($logs, 0, 100);
        
        update_option('aba_activity_logs', $logs);
    }
    
    public function activate() {
        // Create default options
        add_option('aba_api_key', $this->generate_api_key());
        add_option('aba_allowed_ips', array());
        add_option('aba_activity_logs', array());
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    public function add_admin_menu() {
        add_options_page(
            'Automated Blogging API',
            'Blogging API',
            'manage_options',
            'automated-blogging-api',
            array($this, 'admin_page')
        );
    }

    public function admin_init() {
        register_setting('aba_settings', 'aba_api_key');
        register_setting('aba_settings', 'aba_allowed_ips');

        add_settings_section(
            'aba_main_section',
            'API Configuration',
            array($this, 'settings_section_callback'),
            'aba_settings'
        );

        add_settings_field(
            'aba_api_key',
            'API Key',
            array($this, 'api_key_field_callback'),
            'aba_settings',
            'aba_main_section'
        );

        add_settings_field(
            'aba_allowed_ips',
            'Allowed IP Addresses',
            array($this, 'allowed_ips_field_callback'),
            'aba_settings',
            'aba_main_section'
        );
    }

    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>Automated Blogging API Settings</h1>

            <div class="notice notice-info">
                <p><strong>API Endpoint:</strong> <?php echo rest_url('automated-blogging/v1/publish'); ?></p>
                <p><strong>Health Check:</strong> <?php echo rest_url('automated-blogging/v1/health'); ?></p>
            </div>

            <form method="post" action="options.php">
                <?php
                settings_fields('aba_settings');
                do_settings_sections('aba_settings');
                submit_button();
                ?>
            </form>

            <h2>Recent Activity</h2>
            <?php $this->display_activity_logs(); ?>

            <h2>API Documentation</h2>
            <div class="card">
                <h3>Publishing a Post</h3>
                <p><strong>Endpoint:</strong> POST <?php echo rest_url('automated-blogging/v1/publish'); ?></p>
                <p><strong>Headers:</strong></p>
                <ul>
                    <li><code>Content-Type: application/json</code></li>
                    <li><code>X-API-Key: YOUR_API_KEY</code></li>
                </ul>
                <p><strong>Required Parameters:</strong></p>
                <ul>
                    <li><code>title</code> - Post title</li>
                    <li><code>content</code> - Post content (HTML allowed)</li>
                </ul>
                <p><strong>Optional Parameters:</strong></p>
                <ul>
                    <li><code>excerpt</code> - Post excerpt</li>
                    <li><code>slug</code> - Post slug</li>
                    <li><code>categories</code> - Array of category names</li>
                    <li><code>tags</code> - Array of tag names</li>
                    <li><code>meta_description</code> - SEO meta description</li>
                    <li><code>featured_image_url</code> - URL of featured image</li>
                    <li><code>status</code> - Post status (publish, draft, pending)</li>
                    <li><code>author_id</code> - Author ID (default: 1)</li>
                </ul>
            </div>
        </div>
        <?php
    }

    public function settings_section_callback() {
        echo '<p>Configure the API settings for automated blog post publishing.</p>';
    }

    public function api_key_field_callback() {
        $api_key = get_option('aba_api_key');
        echo '<input type="text" id="aba_api_key" name="aba_api_key" value="' . esc_attr($api_key) . '" class="regular-text" readonly />';
        echo '<button type="button" class="button" onclick="generateNewApiKey()">Generate New Key</button>';
        echo '<p class="description">This key is required for API authentication. Keep it secure!</p>';
        ?>
        <script>
        function generateNewApiKey() {
            if (confirm('Are you sure you want to generate a new API key? This will invalidate the current key.')) {
                var newKey = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                document.getElementById('aba_api_key').value = newKey;
            }
        }
        </script>
        <?php
    }

    public function allowed_ips_field_callback() {
        $allowed_ips = get_option('aba_allowed_ips', array());
        $ips_string = is_array($allowed_ips) ? implode("\n", $allowed_ips) : '';
        echo '<textarea id="aba_allowed_ips" name="aba_allowed_ips" rows="5" cols="50" class="large-text">' . esc_textarea($ips_string) . '</textarea>';
        echo '<p class="description">Enter one IP address per line. Leave empty to allow all IPs. Your current IP: ' . $this->get_client_ip() . '</p>';
    }

    private function display_activity_logs() {
        $logs = get_option('aba_activity_logs', array());

        if (empty($logs)) {
            echo '<p>No recent activity.</p>';
            return;
        }

        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>Timestamp</th><th>Action</th><th>Details</th><th>IP Address</th></tr></thead>';
        echo '<tbody>';

        foreach (array_slice($logs, 0, 20) as $log) {
            echo '<tr>';
            echo '<td>' . esc_html($log['timestamp']) . '</td>';
            echo '<td>' . esc_html($log['action']) . '</td>';
            echo '<td>';
            if (!empty($log['data'])) {
                if (isset($log['data']['title'])) {
                    echo 'Title: ' . esc_html($log['data']['title']);
                }
                if (isset($log['data']['post_id'])) {
                    echo ' (ID: ' . esc_html($log['data']['post_id']) . ')';
                }
                if (isset($log['data']['error'])) {
                    echo 'Error: ' . esc_html($log['data']['error']);
                }
            }
            echo '</td>';
            echo '<td>' . esc_html($log['ip']) . '</td>';
            echo '</tr>';
        }

        echo '</tbody></table>';
    }
}

// Initialize the plugin
new AutomatedBloggingAPI();
