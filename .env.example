# Google Sheets Configuration
GOOGLE_SHEETS_ID=your_google_sheets_id_here
GOOGLE_SERVICE_ACCOUNT_KEY_PATH=./config/google-service-account.json

# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# WordPress Configuration
WORDPRESS_SITE_URL=https://your-wordpress-site.com
WORDPRESS_API_KEY=your_wordpress_api_key_here

# Database Configuration
DATABASE_PATH=./data/blogging_system.db

# Server Configuration
PORT=3000
NODE_ENV=development

# Scraping Configuration
SCRAPING_DELAY_MIN=5000
SCRAPING_DELAY_MAX=15000
MAX_RETRIES=3
USER_AGENT_ROTATION=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Scheduler Configuration
PROCESSING_INTERVAL_HOURS=1
MAX_CONCURRENT_JOBS=1

# Image Processing
FEATURED_IMAGE_WIDTH=1200
FEATURED_IMAGE_HEIGHT=630
IMAGE_QUALITY=80

# Security
API_SECRET_KEY=your_secret_key_for_api_authentication
