# 🤖 Automated Blogging System

A comprehensive Node.js application that automatically scrapes job blog URLs from Google Sheets, processes content using Gemini AI, and publishes SEO-optimized articles to WordPress.

## ✨ Features

- **Google Sheets Integration**: Automatically reads URLs from a shared Google Sheet
- **Intelligent Web Scraping**: Handles cookie popups, login overlays, and anti-bot protection
- **AI Content Processing**: Uses Gemini API to create plagiarism-free, SEO-optimized content
- **WordPress Publishing**: Automated posting with categories, tags, and featured images
- **Robust Scheduling**: Hourly processing with queue management and retry logic
- **Monitoring Dashboard**: Real-time system status and job monitoring
- **Error Handling**: Comprehensive logging and failure recovery

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Google Sheets │────│  Node.js System  │────│   WordPress     │
│   (URL Source)  │    │                  │    │   (Publishing)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                       ┌──────────────┐
                       │  Gemini AI   │
                       │ (Processing) │
                       └──────────────┘
```

## 📋 Prerequisites

- Node.js 18.0.0 or higher
- Google Cloud Platform account with Sheets API enabled
- Gemini API key
- WordPress site with the included plugin installed

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd automated-blogging-system
npm install
```

### 2. Environment Configuration

Copy the example environment file and configure:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Google Sheets Configuration
GOOGLE_SHEETS_ID=your_google_sheets_id_here
GOOGLE_SERVICE_ACCOUNT_KEY_PATH=./config/google-service-account.json

# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# WordPress Configuration
WORDPRESS_SITE_URL=https://your-wordpress-site.com
WORDPRESS_API_KEY=your_wordpress_api_key_here

# Database Configuration
DATABASE_PATH=./data/blogging_system.db

# Server Configuration
PORT=3000
NODE_ENV=development
```

### 3. Google Sheets Setup

1. Create a Google Cloud Project
2. Enable the Google Sheets API
3. Create a Service Account and download the JSON key
4. Place the key file at `./config/google-service-account.json`
5. Share your Google Sheet with the service account email
6. Set the `GOOGLE_SHEETS_ID` in your `.env` file

**Required Google Sheet Format:**
```
| URL                           | Status | Notes |
|-------------------------------|--------|-------|
| https://example.com/job1      |        |       |
| https://example.com/job2      |        |       |
```

### 4. WordPress Plugin Installation

1. Upload `wordpress-plugin/automated-blogging-api.php` to your WordPress site
2. Activate the plugin in WordPress Admin
3. Go to Settings > Blogging API to get your API key
4. Add the API key to your `.env` file as `WORDPRESS_API_KEY`

### 5. Start the System

```bash
# Development mode
npm run dev

# Production mode
npm start
```

The dashboard will be available at: `http://localhost:3000/dashboard`

## 📊 Dashboard Features

- **System Status**: Real-time monitoring of processing pipeline
- **Job Management**: View, retry, and manage processing jobs
- **Manual Controls**: Trigger processing and URL fetching manually
- **Connection Testing**: Verify Google Sheets and WordPress connectivity
- **Queue Monitoring**: Track pending and completed jobs

## 🔧 Configuration Options

### Scraping Configuration
```env
SCRAPING_DELAY_MIN=5000          # Minimum delay between requests (ms)
SCRAPING_DELAY_MAX=15000         # Maximum delay between requests (ms)
MAX_RETRIES=3                    # Maximum retry attempts for failed jobs
USER_AGENT_ROTATION=true         # Enable user agent rotation
```

### Scheduler Configuration
```env
PROCESSING_INTERVAL_HOURS=1      # How often to process queue (hours)
MAX_CONCURRENT_JOBS=1            # Maximum concurrent processing jobs
```

### Rate Limiting
```env
RATE_LIMIT_WINDOW_MS=900000      # Rate limit window (15 minutes)
RATE_LIMIT_MAX_REQUESTS=100      # Maximum requests per window
```

## 🔌 API Endpoints

### Public Endpoints
- `GET /health` - System health check
- `GET /dashboard` - Web dashboard

### Authenticated Endpoints (require X-API-Key header)
- `POST /api/jobs` - Add URL to processing queue
- `GET /api/jobs` - List jobs with filtering
- `GET /api/jobs/:id` - Get job details
- `DELETE /api/jobs/:id` - Delete job
- `POST /api/trigger/process` - Manually trigger processing
- `POST /api/trigger/fetch-urls` - Manually fetch URLs from sheets

### Example API Usage

```bash
# Add a URL to the queue
curl -X POST http://localhost:3000/api/jobs \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"url": "https://example.com/job-article"}'

# Get job status
curl http://localhost:3000/api/jobs/1 \
  -H "X-API-Key: your-api-key"
```

## 🛠️ WordPress Plugin Features

The included WordPress plugin provides:

- **Secure API Endpoints**: Authentication via API keys
- **Automatic Post Creation**: With categories, tags, and metadata
- **Featured Image Handling**: Automatic image download and assignment
- **SEO Integration**: Compatible with Yoast and other SEO plugins
- **Activity Logging**: Track all publishing activities
- **IP Whitelisting**: Optional IP-based access control

### Plugin Configuration

1. Install and activate the plugin
2. Go to Settings > Blogging API
3. Copy the generated API key
4. Optionally configure allowed IP addresses
5. Monitor activity in the plugin dashboard

## 📝 Content Processing Pipeline

1. **URL Fetching**: System reads new URLs from Google Sheets every 30 minutes
2. **Web Scraping**: Robust scraping handles various website types and protections
3. **Content Processing**: Gemini AI rewrites content for originality and SEO
4. **WordPress Publishing**: Automated posting with proper metadata and images
5. **Queue Management**: Failed jobs are retried with exponential backoff

## 🔍 Monitoring and Logging

### Log Files
- `logs/combined.log` - All application logs
- `logs/error.log` - Error logs only

### Database Tables
- `jobs` - Processing job records
- `processing_queue` - Job queue management
- `processing_logs` - Detailed step-by-step logs
- `settings` - System configuration

### Dashboard Metrics
- Total jobs processed
- Success/failure rates
- Queue size and processing times
- System health indicators

## 🚨 Troubleshooting

### Common Issues

**Google Sheets Access Denied**
- Verify service account email has access to the sheet
- Check that the Sheets API is enabled in Google Cloud Console
- Ensure the service account key file path is correct

**WordPress Publishing Fails**
- Verify the WordPress plugin is activated
- Check that the API key matches between systems
- Ensure the WordPress site is accessible from the Node.js server

**Scraping Failures**
- Some sites have aggressive anti-bot protection
- Try adjusting scraping delays in configuration
- Check if the target site requires specific headers or cookies

**High Memory Usage**
- Reduce concurrent job processing
- Implement image optimization settings
- Monitor and clean up temporary files

### Debug Mode

Enable debug logging:
```env
LOG_LEVEL=debug
NODE_ENV=development
```

## 🔒 Security Considerations

- Store API keys securely and never commit them to version control
- Use HTTPS for all external communications
- Implement IP whitelisting for production deployments
- Regularly rotate API keys and service account credentials
- Monitor logs for suspicious activity

## 📈 Performance Optimization

- Adjust processing intervals based on your content volume
- Use a dedicated server for production deployments
- Implement caching for frequently accessed data
- Monitor database size and implement cleanup routines
- Consider using a message queue for high-volume processing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review the logs for error details
- Open an issue on the repository
- Consult the API documentation

---

**Note**: This system is designed for legitimate content curation and should be used in compliance with website terms of service and copyright laws. Always ensure you have permission to scrape and republish content.
