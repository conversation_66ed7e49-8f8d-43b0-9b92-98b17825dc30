const cron = require('node-cron');
const database = require('../database/database');
const googleSheets = require('./googleSheets');
const webScraper = require('./webScraper');
const contentProcessor = require('./contentProcessor');
const wordpressPublisher = require('./wordpressPublisher');
const logger = require('../utils/logger');

class SchedulerService {
  constructor() {
    this.isRunning = false;
    this.currentJob = null;
    this.cronJob = null;
    this.processingInterval = parseInt(process.env.PROCESSING_INTERVAL_HOURS) || 1;
    this.maxConcurrentJobs = parseInt(process.env.MAX_CONCURRENT_JOBS) || 1;
    this.maxRetries = parseInt(process.env.MAX_RETRIES) || 3;
  }

  start() {
    try {
      // Schedule the main processing job
      const cronExpression = `0 */${this.processingInterval} * * *`; // Every N hours
      
      this.cronJob = cron.schedule(cronExpression, async () => {
        await this.processQueue();
      }, {
        scheduled: false,
        timezone: 'UTC'
      });

      this.cronJob.start();

      // Also schedule URL fetching from Google Sheets (every 30 minutes)
      cron.schedule('*/30 * * * *', async () => {
        await this.fetchNewURLs();
      }, {
        timezone: 'UTC'
      });

      // Schedule cleanup of old logs (daily at 2 AM)
      cron.schedule('0 2 * * *', async () => {
        await this.cleanupOldLogs();
      }, {
        timezone: 'UTC'
      });

      this.isRunning = true;
      logger.logScheduler('started', {
        processingInterval: `${this.processingInterval} hours`,
        maxConcurrentJobs: this.maxConcurrentJobs
      });

    } catch (error) {
      logger.error('Failed to start scheduler:', error);
      throw error;
    }
  }

  stop() {
    if (this.cronJob) {
      this.cronJob.stop();
    }
    this.isRunning = false;
    logger.logScheduler('stopped');
  }

  async processQueue() {
    if (this.currentJob) {
      logger.logScheduler('skipped', { reason: 'Job already in progress' });
      return;
    }

    try {
      logger.logScheduler('queue_processing_started');

      // Get next job from queue
      const job = await database.getNextJobFromQueue();
      
      if (!job) {
        logger.logScheduler('no_jobs_available');
        return;
      }

      this.currentJob = job;
      await this.processJob(job);

    } catch (error) {
      logger.error('Queue processing error:', error);
    } finally {
      this.currentJob = null;
    }
  }

  async processJob(job) {
    const jobId = job.id;
    const url = job.url;

    try {
      logger.logScheduler('job_started', { jobId, url });

      // Mark job as processing
      await database.updateJob(jobId, { status: 'processing' });
      await database.markQueueItemStarted(jobId);
      await database.addProcessingLog(jobId, 'started', 'success', 'Job processing started');

      // Step 1: Scrape content
      logger.logScheduler('scraping_started', { jobId, url });
      await database.addProcessingLog(jobId, 'scraping', 'started', 'Starting web scraping');

      const scrapedContent = await webScraper.scrapeURL(url);
      
      await database.updateJob(jobId, {
        title: scrapedContent.title,
        content: JSON.stringify(scrapedContent)
      });
      await database.addProcessingLog(jobId, 'scraping', 'success', 'Web scraping completed', {
        title: scrapedContent.title,
        wordCount: scrapedContent.wordCount
      });

      // Step 2: Process content with Gemini
      logger.logScheduler('processing_started', { jobId });
      await database.addProcessingLog(jobId, 'processing', 'started', 'Starting content processing');

      const processedContent = await contentProcessor.processContent(scrapedContent);
      
      await database.updateJob(jobId, {
        processed_content: JSON.stringify(processedContent),
        slug: processedContent.slug,
        meta_description: processedContent.metaDescription,
        categories: JSON.stringify(processedContent.categories),
        tags: JSON.stringify(processedContent.tags)
      });
      await database.addProcessingLog(jobId, 'processing', 'success', 'Content processing completed', {
        seoScore: processedContent.seoScore,
        processedWordCount: processedContent.wordCount
      });

      // Step 3: Publish to WordPress
      logger.logScheduler('publishing_started', { jobId });
      await database.addProcessingLog(jobId, 'publishing', 'started', 'Starting WordPress publishing');

      const publishResult = await wordpressPublisher.publishPost(processedContent, {
        generateFeaturedImage: true
      });

      await database.updateJob(jobId, {
        status: 'completed',
        wordpress_post_id: publishResult.postId,
        featured_image_url: publishResult.featuredImageId ? 'generated' : null,
        processed_at: new Date().toISOString(),
        published_at: new Date().toISOString()
      });
      await database.markQueueItemCompleted(jobId);
      await database.addProcessingLog(jobId, 'publishing', 'success', 'WordPress publishing completed', {
        postId: publishResult.postId,
        postUrl: publishResult.postUrl
      });

      logger.logScheduler('job_completed', {
        jobId,
        url,
        postId: publishResult.postId,
        postUrl: publishResult.postUrl
      });

    } catch (error) {
      logger.error(`Job ${jobId} failed:`, error);

      // Increment retry count
      const currentRetryCount = job.retry_count || 0;
      const newRetryCount = currentRetryCount + 1;

      if (newRetryCount <= this.maxRetries) {
        // Retry the job
        await database.updateJob(jobId, {
          status: 'pending',
          retry_count: newRetryCount,
          error_message: error.message
        });
        await database.addProcessingLog(jobId, 'retry', 'scheduled', `Retry ${newRetryCount}/${this.maxRetries} scheduled`, {
          error: error.message
        });

        // Re-add to queue with lower priority
        await database.addToQueue(jobId, -newRetryCount);

        logger.logScheduler('job_retry_scheduled', {
          jobId,
          retryCount: newRetryCount,
          maxRetries: this.maxRetries
        });
      } else {
        // Mark as failed
        await database.updateJob(jobId, {
          status: 'failed',
          error_message: error.message,
          retry_count: newRetryCount
        });
        await database.addProcessingLog(jobId, 'failed', 'error', 'Job failed after maximum retries', {
          error: error.message,
          retryCount: newRetryCount
        });

        logger.logScheduler('job_failed', {
          jobId,
          url,
          error: error.message,
          retryCount: newRetryCount
        });
      }
    }
  }

  async fetchNewURLs() {
    try {
      logger.logScheduler('url_fetch_started');

      // Get last processed row from settings
      const lastProcessedRow = parseInt(await database.getSetting('last_processed_row')) || 0;

      // Fetch new URLs from Google Sheets
      const newUrls = await googleSheets.getNewURLs(lastProcessedRow);

      if (newUrls.length === 0) {
        logger.logScheduler('no_new_urls');
        return;
      }

      let addedCount = 0;
      let maxRow = lastProcessedRow;

      for (const urlData of newUrls) {
        try {
          // Check if URL already exists
          const existingJob = await database.get('SELECT id FROM jobs WHERE url = ?', [urlData.url]);
          
          if (!existingJob) {
            // Create new job
            const jobId = await database.createJob(urlData.url);
            await database.addToQueue(jobId);
            addedCount++;
            
            logger.info(`Added new URL to queue: ${urlData.url}`);
          }

          // Update max row
          if (urlData.row > maxRow) {
            maxRow = urlData.row;
          }

        } catch (error) {
          logger.error(`Failed to add URL ${urlData.url}:`, error);
        }
      }

      // Update last processed row
      await database.setSetting('last_processed_row', maxRow.toString());

      logger.logScheduler('url_fetch_completed', {
        newUrlsFound: newUrls.length,
        addedToQueue: addedCount,
        lastProcessedRow: maxRow
      });

    } catch (error) {
      logger.error('Failed to fetch new URLs:', error);
    }
  }

  async cleanupOldLogs() {
    try {
      // Keep only logs from last 30 days
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 30);

      await database.run(
        'DELETE FROM processing_logs WHERE created_at < ?',
        [cutoffDate.toISOString()]
      );

      logger.logScheduler('cleanup_completed', {
        cutoffDate: cutoffDate.toISOString()
      });

    } catch (error) {
      logger.error('Failed to cleanup old logs:', error);
    }
  }

  async getStatus() {
    try {
      const stats = await database.getStats();
      const queueSize = await database.get('SELECT COUNT(*) as count FROM processing_queue WHERE started_at IS NULL');
      
      return {
        isRunning: this.isRunning,
        currentJob: this.currentJob ? {
          id: this.currentJob.id,
          url: this.currentJob.url,
          status: this.currentJob.status
        } : null,
        stats: stats,
        queueSize: queueSize.count,
        processingInterval: `${this.processingInterval} hours`,
        maxRetries: this.maxRetries,
        lastProcessedRow: await database.getSetting('last_processed_row') || '0'
      };
    } catch (error) {
      logger.error('Failed to get scheduler status:', error);
      return {
        isRunning: this.isRunning,
        error: error.message
      };
    }
  }

  // Manual trigger for testing
  async triggerProcessing() {
    if (this.currentJob) {
      throw new Error('Processing already in progress');
    }

    logger.logScheduler('manual_trigger');
    await this.processQueue();
  }

  // Manual URL fetch for testing
  async triggerURLFetch() {
    logger.logScheduler('manual_url_fetch');
    await this.fetchNewURLs();
  }
}

module.exports = new SchedulerService();
