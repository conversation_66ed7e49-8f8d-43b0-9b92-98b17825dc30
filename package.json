{"name": "automated-blogging-system", "version": "1.0.0", "description": "Automated blogging system that scrapes job URLs from Google Sheets and publishes to WordPress", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "lint": "eslint src/", "setup": "node scripts/setup.js"}, "keywords": ["automation", "blogging", "wordpress", "web-scraping", "google-sheets", "gemini-api"], "author": "Automated Blogging System", "license": "MIT", "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.5.0", "googleapis": "^128.0.0", "node-cron": "^3.0.3", "sqlite3": "^5.1.6", "dotenv": "^16.3.1", "winston": "^3.11.0", "helmet": "^7.1.0", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "rate-limiter-flexible": "^3.0.8", "user-agents": "^1.0.1", "proxy-agent": "^6.3.1", "@google/generative-ai": "^0.2.1", "turndown": "^7.1.2", "slug": "^8.2.3", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.3.1", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=18.0.0"}}