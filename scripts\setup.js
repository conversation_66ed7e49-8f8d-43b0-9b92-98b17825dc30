#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const crypto = require('crypto');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupSystem() {
  console.log('🤖 Automated Blogging System Setup\n');
  console.log('This script will help you configure the system for first use.\n');

  // Check if .env already exists
  if (fs.existsSync('.env')) {
    const overwrite = await question('⚠️  .env file already exists. Overwrite? (y/N): ');
    if (overwrite.toLowerCase() !== 'y') {
      console.log('Setup cancelled.');
      rl.close();
      return;
    }
  }

  console.log('📋 Please provide the following information:\n');

  // Collect configuration
  const config = {};

  // Google Sheets Configuration
  console.log('🔗 Google Sheets Configuration:');
  config.GOOGLE_SHEETS_ID = await question('Google Sheets ID: ');
  
  const serviceAccountPath = await question('Service account key file path (./config/google-service-account.json): ');
  config.GOOGLE_SERVICE_ACCOUNT_KEY_PATH = serviceAccountPath || './config/google-service-account.json';

  // Gemini API Configuration
  console.log('\n🤖 Gemini API Configuration:');
  config.GEMINI_API_KEY = await question('Gemini API Key: ');

  // WordPress Configuration
  console.log('\n🌐 WordPress Configuration:');
  config.WORDPRESS_SITE_URL = await question('WordPress Site URL (https://example.com): ');
  config.WORDPRESS_API_KEY = await question('WordPress API Key: ');

  // Server Configuration
  console.log('\n⚙️  Server Configuration:');
  const port = await question('Server Port (3000): ');
  config.PORT = port || '3000';

  const nodeEnv = await question('Environment (development/production) [development]: ');
  config.NODE_ENV = nodeEnv || 'development';

  // Generate API Secret Key
  config.API_SECRET_KEY = crypto.randomBytes(32).toString('hex');
  console.log(`\n🔐 Generated API Secret Key: ${config.API_SECRET_KEY}`);

  // Optional configurations with defaults
  console.log('\n📊 Optional Configuration (press Enter for defaults):');
  
  const scrapingDelayMin = await question('Scraping delay minimum (5000ms): ');
  config.SCRAPING_DELAY_MIN = scrapingDelayMin || '5000';

  const scrapingDelayMax = await question('Scraping delay maximum (15000ms): ');
  config.SCRAPING_DELAY_MAX = scrapingDelayMax || '15000';

  const maxRetries = await question('Maximum retries (3): ');
  config.MAX_RETRIES = maxRetries || '3';

  const processingInterval = await question('Processing interval hours (1): ');
  config.PROCESSING_INTERVAL_HOURS = processingInterval || '1';

  const maxConcurrentJobs = await question('Maximum concurrent jobs (1): ');
  config.MAX_CONCURRENT_JOBS = maxConcurrentJobs || '1';

  // Database path
  config.DATABASE_PATH = './data/blogging_system.db';

  // Rate limiting
  config.RATE_LIMIT_WINDOW_MS = '900000';
  config.RATE_LIMIT_MAX_REQUESTS = '100';

  // Logging
  config.LOG_LEVEL = config.NODE_ENV === 'production' ? 'info' : 'debug';
  config.LOG_FILE_PATH = './logs/app.log';

  // Image processing
  config.FEATURED_IMAGE_WIDTH = '1200';
  config.FEATURED_IMAGE_HEIGHT = '630';
  config.IMAGE_QUALITY = '80';

  // Create .env file
  console.log('\n📝 Creating .env file...');
  
  const envContent = Object.entries(config)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');

  fs.writeFileSync('.env', envContent);

  // Create required directories
  console.log('📁 Creating required directories...');
  const directories = ['config', 'data', 'logs', 'temp'];
  
  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`   ✅ Created ${dir}/`);
    } else {
      console.log(`   ℹ️  ${dir}/ already exists`);
    }
  });

  // Check for service account file
  console.log('\n🔑 Checking service account file...');
  if (fs.existsSync(config.GOOGLE_SERVICE_ACCOUNT_KEY_PATH)) {
    console.log('   ✅ Service account file found');
  } else {
    console.log(`   ⚠️  Service account file not found at: ${config.GOOGLE_SERVICE_ACCOUNT_KEY_PATH}`);
    console.log('   Please download your Google Cloud service account key and place it at the specified path.');
  }

  // Create sample Google Sheet template
  console.log('\n📊 Creating Google Sheet template...');
  const sheetTemplate = `Google Sheet Template for Automated Blogging System

Required columns (Row 1):
URL | Status | Title | Notes

Example data:
https://example.com/job-article-1 | | | Sample job posting
https://example.com/job-article-2 | | | Another job posting

Instructions:
1. Create a new Google Sheet
2. Add the columns above in row 1
3. Add URLs in column A starting from row 2
4. Share the sheet with your service account email
5. Copy the sheet ID from the URL and add it to your .env file

Service Account Email: Check your service account key file for the "client_email" field
`;

  fs.writeFileSync('google-sheet-template.txt', sheetTemplate);
  console.log('   ✅ Created google-sheet-template.txt');

  // Installation summary
  console.log('\n🎉 Setup Complete!\n');
  console.log('Next steps:');
  console.log('1. Place your Google Cloud service account key at:', config.GOOGLE_SERVICE_ACCOUNT_KEY_PATH);
  console.log('2. Set up your Google Sheet using the template in google-sheet-template.txt');
  console.log('3. Install and configure the WordPress plugin from wordpress-plugin/');
  console.log('4. Run: npm install (if not already done)');
  console.log('5. Run: npm run dev');
  console.log('6. Visit: http://localhost:' + config.PORT + '/dashboard');
  console.log('\nFor detailed setup instructions, see SETUP.md');

  rl.close();
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('\n❌ Setup failed:', error.message);
  rl.close();
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n\n👋 Setup cancelled by user');
  rl.close();
  process.exit(0);
});

// Run setup
setupSystem().catch(error => {
  console.error('\n❌ Setup failed:', error.message);
  process.exit(1);
});
