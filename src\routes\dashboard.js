const express = require('express');
const path = require('path');
const database = require('../database/database');
const scheduler = require('../services/scheduler');
const googleSheets = require('../services/googleSheets');
const wordpressPublisher = require('../services/wordpressPublisher');
const logger = require('../utils/logger');

const router = express.Router();

// Serve dashboard HTML
router.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/dashboard.html'));
});

// Get system status
router.get('/api/status', async (req, res) => {
  try {
    const schedulerStatus = await scheduler.getStatus();
    const dbStats = await database.getStats();
    
    // Get recent jobs
    const recentJobs = await database.all(`
      SELECT id, url, status, title, created_at, updated_at, retry_count, error_message
      FROM jobs 
      ORDER BY updated_at DESC 
      LIMIT 10
    `);

    // Get queue status
    const queueStatus = await database.get(`
      SELECT COUNT(*) as pending_count
      FROM processing_queue pq
      JOIN jobs j ON pq.job_id = j.id
      WHERE j.status = 'pending' AND pq.started_at IS NULL
    `);

    res.json({
      scheduler: schedulerStatus,
      database: dbStats,
      recentJobs: recentJobs,
      queueSize: queueStatus.pending_count,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to get system status:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get detailed job information
router.get('/api/jobs/:id', async (req, res) => {
  try {
    const jobId = req.params.id;
    const job = await database.getJob(jobId);
    
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }

    // Get processing logs for this job
    const logs = await database.getProcessingLogs(jobId);

    // Parse JSON fields
    if (job.content) {
      try {
        job.content = JSON.parse(job.content);
      } catch (e) {
        // Keep as string if not valid JSON
      }
    }

    if (job.processed_content) {
      try {
        job.processed_content = JSON.parse(job.processed_content);
      } catch (e) {
        // Keep as string if not valid JSON
      }
    }

    if (job.categories) {
      try {
        job.categories = JSON.parse(job.categories);
      } catch (e) {
        job.categories = [];
      }
    }

    if (job.tags) {
      try {
        job.tags = JSON.parse(job.tags);
      } catch (e) {
        job.tags = [];
      }
    }

    res.json({
      job: job,
      logs: logs
    });

  } catch (error) {
    logger.error('Failed to get job details:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get all jobs with pagination
router.get('/api/jobs', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const status = req.query.status;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (status && status !== 'all') {
      whereClause = 'WHERE status = ?';
      params.push(status);
    }

    const jobs = await database.all(`
      SELECT id, url, status, title, created_at, updated_at, retry_count, 
             wordpress_post_id, error_message
      FROM jobs 
      ${whereClause}
      ORDER BY updated_at DESC 
      LIMIT ? OFFSET ?
    `, [...params, limit, offset]);

    // Get total count
    const totalResult = await database.get(`
      SELECT COUNT(*) as total FROM jobs ${whereClause}
    `, params);

    res.json({
      jobs: jobs,
      pagination: {
        page: page,
        limit: limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    });

  } catch (error) {
    logger.error('Failed to get jobs:', error);
    res.status(500).json({ error: error.message });
  }
});

// Manual trigger processing
router.post('/api/trigger/processing', async (req, res) => {
  try {
    await scheduler.triggerProcessing();
    res.json({ success: true, message: 'Processing triggered successfully' });
  } catch (error) {
    logger.error('Failed to trigger processing:', error);
    res.status(500).json({ error: error.message });
  }
});

// Manual trigger URL fetch
router.post('/api/trigger/url-fetch', async (req, res) => {
  try {
    await scheduler.triggerURLFetch();
    res.json({ success: true, message: 'URL fetch triggered successfully' });
  } catch (error) {
    logger.error('Failed to trigger URL fetch:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test Google Sheets connection
router.post('/api/test/google-sheets', async (req, res) => {
  try {
    const isValid = await googleSheets.validateSheetAccess();
    
    if (isValid) {
      const info = await googleSheets.getSheetInfo();
      const sampleData = await googleSheets.getSampleData(3);
      const columnInfo = await googleSheets.detectColumns();

      res.json({
        success: true,
        message: 'Google Sheets connection successful',
        data: {
          sheetInfo: info,
          sampleData: sampleData,
          columnInfo: columnInfo
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Google Sheets connection failed'
      });
    }

  } catch (error) {
    logger.error('Google Sheets test failed:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test WordPress connection
router.post('/api/test/wordpress', async (req, res) => {
  try {
    const result = await wordpressPublisher.testConnection();
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    logger.error('WordPress test failed:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Publish test post to WordPress
router.post('/api/test/wordpress-publish', async (req, res) => {
  try {
    const result = await wordpressPublisher.publishTestPost();
    res.json({
      success: true,
      message: 'Test post published successfully',
      data: result
    });

  } catch (error) {
    logger.error('WordPress test publish failed:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get system logs
router.get('/api/logs', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const level = req.query.level || 'all';

    // This is a simplified log endpoint
    // In production, you might want to read from log files or use a proper logging service
    const logs = await database.all(`
      SELECT * FROM processing_logs 
      ORDER BY created_at DESC 
      LIMIT ?
    `, [limit]);

    res.json({
      logs: logs,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to get logs:', error);
    res.status(500).json({ error: error.message });
  }
});

// Add new URL manually
router.post('/api/jobs', async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return res.status(400).json({ error: 'Invalid URL format' });
    }

    // Check if URL already exists
    const existingJob = await database.get('SELECT id FROM jobs WHERE url = ?', [url]);
    if (existingJob) {
      return res.status(400).json({ error: 'URL already exists in the system' });
    }

    // Create new job
    const jobId = await database.createJob(url);
    await database.addToQueue(jobId);

    logger.info(`Manually added URL to queue: ${url}`);

    res.json({
      success: true,
      message: 'URL added to queue successfully',
      jobId: jobId
    });

  } catch (error) {
    logger.error('Failed to add URL:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete job
router.delete('/api/jobs/:id', async (req, res) => {
  try {
    const jobId = req.params.id;
    
    // Check if job exists
    const job = await database.getJob(jobId);
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }

    // Delete job and related records
    await database.run('DELETE FROM processing_logs WHERE job_id = ?', [jobId]);
    await database.run('DELETE FROM processing_queue WHERE job_id = ?', [jobId]);
    await database.run('DELETE FROM jobs WHERE id = ?', [jobId]);

    logger.info(`Deleted job ${jobId}: ${job.url}`);

    res.json({
      success: true,
      message: 'Job deleted successfully'
    });

  } catch (error) {
    logger.error('Failed to delete job:', error);
    res.status(500).json({ error: error.message });
  }
});

// Retry failed job
router.post('/api/jobs/:id/retry', async (req, res) => {
  try {
    const jobId = req.params.id;
    
    // Check if job exists and is failed
    const job = await database.getJob(jobId);
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }

    if (job.status !== 'failed') {
      return res.status(400).json({ error: 'Only failed jobs can be retried' });
    }

    // Reset job status and add back to queue
    await database.updateJob(jobId, {
      status: 'pending',
      retry_count: 0,
      error_message: null
    });
    await database.addToQueue(jobId);
    await database.addProcessingLog(jobId, 'retry', 'manual', 'Job manually retried');

    logger.info(`Manually retried job ${jobId}: ${job.url}`);

    res.json({
      success: true,
      message: 'Job queued for retry successfully'
    });

  } catch (error) {
    logger.error('Failed to retry job:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
