# 📊 Google Sheet Template

This document provides the template and setup instructions for the Google Sheet that feeds URLs to the Automated Blogging System.

## 📋 Required Sheet Structure

Your Google Sheet must have the following columns in **Row 1** (header row):

| Column A | Column B | Column C | Column D |
|----------|----------|----------|----------|
| **URL**  | Status   | Title    | Notes    |

### Column Descriptions

- **URL** (Required): The full URL of the article to be processed
- **Status**: Optional field for tracking (system doesn't use this)
- **Title**: Optional field for reference (system will extract actual title)
- **Notes**: Optional field for your internal notes

## 📝 Example Sheet Content

```
Row 1: URL                                    | Status    | Title                           | Notes
Row 2: https://example.com/job-posting-1      |           | Software Engineer Position     | High priority
Row 3: https://example.com/career-advice-1    |           | Career Development Tips         | 
Row 4: https://jobboard.com/remote-work       | Processed | Remote Work Opportunities       | Already processed
Row 5: https://careers.company.com/new-role   |           |                                 | New addition
```

## 🔗 Google Sheet Setup Instructions

### Step 1: Create the Sheet

1. Go to [Google Sheets](https://sheets.google.com)
2. Click "Blank" to create a new spreadsheet
3. Name it something like "Blog URLs Queue" or "Automated Blogging URLs"

### Step 2: Set Up Headers

1. In cell A1, type: `URL`
2. In cell B1, type: `Status`
3. In cell C1, type: `Title`
4. In cell D1, type: `Notes`

### Step 3: Add Your URLs

Starting from Row 2, add your URLs in column A:

```
A2: https://example.com/first-article
A3: https://example.com/second-article
A4: https://example.com/third-article
```

### Step 4: Share with Service Account

1. Click the "Share" button in the top right
2. Add your service account email (found in your Google Cloud service account JSON file)
3. Set permission to "Editor"
4. Uncheck "Notify people"
5. Click "Share"

### Step 5: Get Sheet ID

Copy the Sheet ID from your browser's address bar:

```
https://docs.google.com/spreadsheets/d/SHEET_ID_HERE/edit#gid=0
```

The SHEET_ID_HERE part is what you need for your `.env` file.

## ✅ URL Requirements

### Valid URL Format

URLs must be complete and valid:

✅ **Good Examples:**
- `https://example.com/job-posting`
- `https://careers.company.com/software-engineer`
- `http://jobboard.org/remote-opportunities`

❌ **Bad Examples:**
- `example.com/job-posting` (missing protocol)
- `www.example.com` (missing protocol)
- `https://` (incomplete URL)
- `not-a-url` (invalid format)

### Supported Content Types

The system works best with:
- Job posting articles
- Career advice content
- Industry news and insights
- Professional development articles
- Company blog posts about careers

## 🔄 How the System Uses Your Sheet

### Automatic Processing

1. **Every 30 minutes**: The system checks for new URLs
2. **Row tracking**: It remembers the last processed row
3. **New URLs only**: Only processes URLs that haven't been seen before
4. **Sequential processing**: Processes one URL at a time every hour

### Status Tracking

The system tracks URL processing in its internal database, not in your Google Sheet. You can use the Status column for your own reference.

## 📊 Sheet Management Best Practices

### Adding New URLs

- Always add new URLs at the bottom of the sheet
- Don't insert rows in the middle (this can confuse the row tracking)
- Add URLs one per row in column A

### Organizing Your Content

- Use the Notes column to categorize content
- Use the Title column for quick reference
- Consider color-coding rows for different content types

### Monitoring Progress

- Check the system dashboard to see processing status
- URLs are processed in the order they appear in the sheet
- Failed URLs will be retried automatically

## 🚨 Common Issues and Solutions

### "No new URLs found"

**Possible causes:**
- All URLs in the sheet have already been processed
- The sheet is empty or has no valid URLs
- Service account doesn't have access to the sheet

**Solutions:**
- Add new URLs to the bottom of the sheet
- Check that URLs are in column A and are valid
- Verify service account permissions

### "Google Sheets access denied"

**Possible causes:**
- Service account email not added to sheet sharing
- Service account key file is missing or invalid
- Google Sheets API not enabled

**Solutions:**
- Re-share the sheet with the service account email
- Check the service account key file path
- Verify API is enabled in Google Cloud Console

### "Invalid URL format"

**Possible causes:**
- URLs missing http:// or https://
- Malformed URLs
- Empty cells in the URL column

**Solutions:**
- Ensure all URLs start with http:// or https://
- Check for typos in URLs
- Remove empty rows between URLs

## 📈 Scaling Your Operation

### For High Volume

- Consider multiple sheets for different content categories
- Use the Notes column to prioritize content
- Monitor the processing queue size in the dashboard

### Content Organization

- Create separate sheets for different content types
- Use consistent naming conventions
- Document your URL sources for future reference

## 🔒 Security Considerations

- Never share your service account key file
- Only share the Google Sheet with necessary accounts
- Regularly review who has access to your sheet
- Use HTTPS URLs whenever possible

## 📞 Support

If you encounter issues with your Google Sheet setup:

1. Check the system logs for specific error messages
2. Verify all URLs are valid and accessible
3. Test the Google Sheets connection in the dashboard
4. Consult the main troubleshooting guide in README.md

---

**Template Download**: You can copy this template structure to create your own sheet, or use the setup script to generate a template file automatically.
