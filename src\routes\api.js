const express = require('express');
const { RateLimiterMemory } = require('rate-limiter-flexible');
const database = require('../database/database');
const scheduler = require('../services/scheduler');
const webScraper = require('../services/webScraper');
const contentProcessor = require('../services/contentProcessor');
const wordpressPublisher = require('../services/wordpressPublisher');
const googleSheets = require('../services/googleSheets');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiter for API endpoints
const rateLimiter = new RateLimiterMemory({
  keyGenerator: (req) => req.ip,
  points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
});

// Rate limiting middleware
const rateLimitMiddleware = async (req, res, next) => {
  try {
    await rateLimiter.consume(req.ip);
    next();
  } catch (rejRes) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      error: 'Too Many Requests',
      message: `Rate limit exceeded. Try again in ${secs} seconds.`
    });
  }
};

// Apply rate limiting to all API routes
router.use(rateLimitMiddleware);

// API key authentication middleware
const authenticateAPI = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  const expectedKey = process.env.API_SECRET_KEY;

  if (!expectedKey) {
    return res.status(500).json({
      error: 'Server configuration error',
      message: 'API authentication not configured'
    });
  }

  if (!apiKey || apiKey !== expectedKey) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid or missing API key'
    });
  }

  next();
};

// Health check endpoint (no auth required)
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    uptime: process.uptime()
  });
});

// Get system status (requires auth)
router.get('/status', authenticateAPI, async (req, res) => {
  try {
    const status = await scheduler.getStatus();
    const stats = await database.getStats();

    res.json({
      scheduler: status,
      database: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('API status error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Add URL to processing queue
router.post('/jobs', authenticateAPI, async (req, res) => {
  try {
    const { url, priority = 0 } = req.body;

    if (!url) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'URL is required'
      });
    }

    // Validate URL format
    try {
      new URL(url);
    } catch (e) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid URL format'
      });
    }

    // Check if URL already exists
    const existingJob = await database.get('SELECT id, status FROM jobs WHERE url = ?', [url]);
    if (existingJob) {
      return res.status(409).json({
        error: 'Conflict',
        message: 'URL already exists in the system',
        jobId: existingJob.id,
        status: existingJob.status
      });
    }

    // Create new job
    const jobId = await database.createJob(url);
    await database.addToQueue(jobId, priority);

    logger.info(`API: Added URL to queue: ${url}`);

    res.status(201).json({
      success: true,
      message: 'URL added to processing queue',
      jobId: jobId,
      url: url
    });

  } catch (error) {
    logger.error('API add job error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get job status
router.get('/jobs/:id', authenticateAPI, async (req, res) => {
  try {
    const jobId = req.params.id;
    const job = await database.getJob(jobId);

    if (!job) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Job not found'
      });
    }

    // Parse JSON fields for API response
    const response = {
      id: job.id,
      url: job.url,
      status: job.status,
      title: job.title,
      slug: job.slug,
      wordpress_post_id: job.wordpress_post_id,
      retry_count: job.retry_count,
      error_message: job.error_message,
      created_at: job.created_at,
      updated_at: job.updated_at,
      processed_at: job.processed_at,
      published_at: job.published_at
    };

    // Include processed content if available and requested
    if (req.query.include_content === 'true' && job.processed_content) {
      try {
        response.processed_content = JSON.parse(job.processed_content);
      } catch (e) {
        response.processed_content = job.processed_content;
      }
    }

    res.json(response);

  } catch (error) {
    logger.error('API get job error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get jobs list with filtering
router.get('/jobs', authenticateAPI, async (req, res) => {
  try {
    const {
      status,
      limit = 20,
      offset = 0,
      sort = 'updated_at',
      order = 'DESC'
    } = req.query;

    let whereClause = '';
    let params = [];

    if (status && status !== 'all') {
      whereClause = 'WHERE status = ?';
      params.push(status);
    }

    // Validate sort and order parameters
    const allowedSorts = ['id', 'url', 'status', 'created_at', 'updated_at'];
    const allowedOrders = ['ASC', 'DESC'];

    const sortField = allowedSorts.includes(sort) ? sort : 'updated_at';
    const sortOrder = allowedOrders.includes(order.toUpperCase()) ? order.toUpperCase() : 'DESC';

    const jobs = await database.all(`
      SELECT id, url, status, title, wordpress_post_id, retry_count, 
             created_at, updated_at, processed_at, published_at
      FROM jobs 
      ${whereClause}
      ORDER BY ${sortField} ${sortOrder}
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // Get total count
    const totalResult = await database.get(`
      SELECT COUNT(*) as total FROM jobs ${whereClause}
    `, params);

    res.json({
      jobs: jobs,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: totalResult.total
      }
    });

  } catch (error) {
    logger.error('API get jobs error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Trigger manual processing
router.post('/trigger/process', authenticateAPI, async (req, res) => {
  try {
    await scheduler.triggerProcessing();
    res.json({
      success: true,
      message: 'Processing triggered successfully'
    });
  } catch (error) {
    logger.error('API trigger processing error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Trigger URL fetch from Google Sheets
router.post('/trigger/fetch-urls', authenticateAPI, async (req, res) => {
  try {
    await scheduler.triggerURLFetch();
    res.json({
      success: true,
      message: 'URL fetch triggered successfully'
    });
  } catch (error) {
    logger.error('API trigger URL fetch error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test scraping a URL
router.post('/test/scrape', authenticateAPI, async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'URL is required'
      });
    }

    const result = await webScraper.testScrape(url);
    res.json(result);

  } catch (error) {
    logger.error('API test scrape error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test content processing
router.post('/test/process', authenticateAPI, async (req, res) => {
  try {
    const { title, content } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Title and content are required'
      });
    }

    const result = await contentProcessor.testProcessing(content, title);
    res.json(result);

  } catch (error) {
    logger.error('API test processing error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test WordPress publishing
router.post('/test/wordpress', authenticateAPI, async (req, res) => {
  try {
    const result = await wordpressPublisher.testConnection();
    res.json(result);

  } catch (error) {
    logger.error('API test WordPress error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test Google Sheets connection
router.post('/test/sheets', authenticateAPI, async (req, res) => {
  try {
    const isValid = await googleSheets.validateSheetAccess();
    
    if (isValid) {
      const info = await googleSheets.getSheetInfo();
      res.json({
        success: true,
        message: 'Google Sheets connection successful',
        data: info
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Google Sheets connection failed'
      });
    }

  } catch (error) {
    logger.error('API test Google Sheets error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get processing logs for a job
router.get('/jobs/:id/logs', authenticateAPI, async (req, res) => {
  try {
    const jobId = req.params.id;
    
    // Check if job exists
    const job = await database.getJob(jobId);
    if (!job) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Job not found'
      });
    }

    const logs = await database.getProcessingLogs(jobId);
    res.json({
      jobId: jobId,
      logs: logs
    });

  } catch (error) {
    logger.error('API get job logs error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete a job
router.delete('/jobs/:id', authenticateAPI, async (req, res) => {
  try {
    const jobId = req.params.id;
    
    // Check if job exists
    const job = await database.getJob(jobId);
    if (!job) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Job not found'
      });
    }

    // Delete job and related records
    await database.run('DELETE FROM processing_logs WHERE job_id = ?', [jobId]);
    await database.run('DELETE FROM processing_queue WHERE job_id = ?', [jobId]);
    await database.run('DELETE FROM jobs WHERE id = ?', [jobId]);

    logger.info(`API: Deleted job ${jobId}: ${job.url}`);

    res.json({
      success: true,
      message: 'Job deleted successfully'
    });

  } catch (error) {
    logger.error('API delete job error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
