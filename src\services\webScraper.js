const puppeteer = require('puppeteer');
const cheerio = require('cheerio');
const axios = require('axios');
const UserAgent = require('user-agents');
const { RateLimiterMemory } = require('rate-limiter-flexible');
const logger = require('../utils/logger');

class WebScraperService {
  constructor() {
    this.browser = null;
    this.userAgent = new UserAgent();
    
    // Rate limiter to avoid being blocked
    this.rateLimiter = new RateLimiterMemory({
      keyGenerator: (req) => req.hostname || 'default',
      points: 5, // Number of requests
      duration: 60, // Per 60 seconds
    });

    this.defaultOptions = {
      timeout: 30000,
      waitForSelector: 'body',
      blockResources: ['image', 'stylesheet', 'font', 'media'],
      userAgent: this.userAgent.toString()
    };
  }

  async initialize() {
    try {
      this.browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      });
      logger.info('Web scraper initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize web scraper:', error);
      throw error;
    }
  }

  async scrapeURL(url, options = {}) {
    const startTime = Date.now();
    let page = null;

    try {
      // Apply rate limiting
      const hostname = new URL(url).hostname;
      await this.rateLimiter.consume(hostname);

      // Initialize browser if not already done
      if (!this.browser) {
        await this.initialize();
      }

      // Create new page
      page = await this.browser.newPage();
      
      // Set user agent
      await page.setUserAgent(options.userAgent || this.defaultOptions.userAgent);

      // Block unnecessary resources to speed up loading
      if (options.blockResources !== false) {
        await page.setRequestInterception(true);
        page.on('request', (req) => {
          const resourceType = req.resourceType();
          const blockedTypes = options.blockResources || this.defaultOptions.blockResources;
          
          if (blockedTypes.includes(resourceType)) {
            req.abort();
          } else {
            req.continue();
          }
        });
      }

      // Set viewport
      await page.setViewport({ width: 1366, height: 768 });

      // Navigate to URL with timeout
      const response = await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: options.timeout || this.defaultOptions.timeout
      });

      if (!response.ok()) {
        throw new Error(`HTTP ${response.status()}: ${response.statusText()}`);
      }

      // Handle cookie consent popups
      await this.handleCookieConsent(page);

      // Handle login overlays
      await this.handleLoginOverlays(page);

      // Wait for content to load
      await this.waitForContent(page, options);

      // Extract content
      const content = await this.extractContent(page, url);

      const processingTime = Date.now() - startTime;
      logger.logScraping(url, 'success', { 
        processingTime: `${processingTime}ms`,
        contentLength: content.text.length,
        title: content.title
      });

      return content;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.logScraping(url, 'error', { 
        processingTime: `${processingTime}ms`,
        error: error.message 
      });

      // Try fallback method with axios + cheerio
      if (options.fallback !== false) {
        logger.info(`Trying fallback scraping method for: ${url}`);
        return await this.fallbackScrape(url);
      }

      throw error;
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  async handleCookieConsent(page) {
    const cookieSelectors = [
      'button[id*="accept"]',
      'button[class*="accept"]',
      'button[id*="cookie"]',
      'button[class*="cookie"]',
      'button[id*="consent"]',
      'button[class*="consent"]',
      '.cookie-accept',
      '.accept-cookies',
      '#accept-cookies',
      '[data-testid*="accept"]',
      '[data-testid*="cookie"]'
    ];

    for (const selector of cookieSelectors) {
      try {
        const element = await page.$(selector);
        if (element) {
          await element.click();
          logger.info('Clicked cookie consent button');
          await page.waitForTimeout(1000);
          break;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
  }

  async handleLoginOverlays(page) {
    const overlaySelectors = [
      '.modal-close',
      '.close-modal',
      '.overlay-close',
      '[aria-label="Close"]',
      '[data-dismiss="modal"]',
      '.popup-close',
      'button[class*="close"]'
    ];

    for (const selector of overlaySelectors) {
      try {
        const element = await page.$(selector);
        if (element) {
          await element.click();
          logger.info('Closed overlay/modal');
          await page.waitForTimeout(1000);
          break;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
  }

  async waitForContent(page, options) {
    const waitSelectors = [
      'article',
      '.content',
      '.post-content',
      '.entry-content',
      '.article-content',
      'main',
      '[role="main"]'
    ];

    // Try to wait for main content selectors
    for (const selector of waitSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 });
        break;
      } catch (error) {
        // Continue to next selector
      }
    }

    // Wait for any additional loading
    await page.waitForTimeout(2000);
  }

  async extractContent(page, url) {
    const content = await page.evaluate(() => {
      // Remove unwanted elements
      const unwantedSelectors = [
        'script', 'style', 'nav', 'header', 'footer', 
        '.advertisement', '.ads', '.sidebar', '.menu',
        '.social-share', '.comments', '.related-posts',
        '[class*="ad-"]', '[id*="ad-"]', '.popup', '.modal'
      ];

      unwantedSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => el.remove());
      });

      // Try to find main content
      const contentSelectors = [
        'article',
        '.post-content',
        '.entry-content',
        '.article-content',
        '.content',
        'main',
        '[role="main"]',
        '.post-body'
      ];

      let mainContent = null;
      for (const selector of contentSelectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent.trim().length > 200) {
          mainContent = element;
          break;
        }
      }

      // Fallback to body if no main content found
      if (!mainContent) {
        mainContent = document.body;
      }

      // Extract title
      const title = document.querySelector('h1')?.textContent?.trim() ||
                   document.querySelector('title')?.textContent?.trim() ||
                   document.querySelector('.post-title')?.textContent?.trim() ||
                   document.querySelector('.entry-title')?.textContent?.trim() ||
                   'Untitled';

      // Extract meta description
      const metaDesc = document.querySelector('meta[name="description"]')?.getAttribute('content') ||
                      document.querySelector('meta[property="og:description"]')?.getAttribute('content') ||
                      '';

      // Extract images
      const images = Array.from(mainContent.querySelectorAll('img')).map(img => ({
        src: img.src,
        alt: img.alt || '',
        title: img.title || ''
      }));

      // Clean and extract text content
      const textContent = mainContent.textContent
        .replace(/\s+/g, ' ')
        .replace(/\n\s*\n/g, '\n')
        .trim();

      // Extract HTML content
      const htmlContent = mainContent.innerHTML;

      return {
        title: title,
        text: textContent,
        html: htmlContent,
        metaDescription: metaDesc,
        images: images,
        wordCount: textContent.split(/\s+/).length
      };
    });

    return {
      ...content,
      url: url,
      scrapedAt: new Date().toISOString()
    };
  }

  async fallbackScrape(url) {
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': this.userAgent.toString(),
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive'
        },
        timeout: 15000
      });

      const $ = cheerio.load(response.data);

      // Remove unwanted elements
      $('script, style, nav, header, footer, .advertisement, .ads, .sidebar').remove();

      // Extract title
      const title = $('h1').first().text().trim() ||
                   $('title').text().trim() ||
                   $('.post-title, .entry-title').first().text().trim() ||
                   'Untitled';

      // Extract main content
      let content = '';
      const contentSelectors = ['article', '.post-content', '.entry-content', '.content', 'main'];
      
      for (const selector of contentSelectors) {
        const element = $(selector).first();
        if (element.length && element.text().trim().length > 200) {
          content = element.text().trim();
          break;
        }
      }

      if (!content) {
        content = $('body').text().trim();
      }

      // Extract meta description
      const metaDescription = $('meta[name="description"]').attr('content') ||
                             $('meta[property="og:description"]').attr('content') ||
                             '';

      // Extract images
      const images = [];
      $('img').each((i, img) => {
        const src = $(img).attr('src');
        if (src) {
          images.push({
            src: src,
            alt: $(img).attr('alt') || '',
            title: $(img).attr('title') || ''
          });
        }
      });

      logger.logScraping(url, 'fallback_success', { 
        contentLength: content.length,
        title: title
      });

      return {
        title: title,
        text: content.replace(/\s+/g, ' ').trim(),
        html: '', // Not available in fallback mode
        metaDescription: metaDescription,
        images: images,
        wordCount: content.split(/\s+/).length,
        url: url,
        scrapedAt: new Date().toISOString()
      };

    } catch (error) {
      logger.logScraping(url, 'fallback_error', { error: error.message });
      throw error;
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      logger.info('Web scraper browser closed');
    }
  }

  // Method to test scraping capabilities
  async testScrape(url) {
    try {
      const result = await this.scrapeURL(url, { fallback: true });
      return {
        success: true,
        data: result,
        message: 'Scraping test successful'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Scraping test failed'
      };
    }
  }
}

module.exports = new WebScraperService();
