# 🔌 API Documentation

This document provides comprehensive documentation for the Automated Blogging System API endpoints.

## 🔐 Authentication

Most API endpoints require authentication using an API key. Include the key in the request header:

```
X-API-Key: your-api-secret-key
```

The API key is set in your `.env` file as `API_SECRET_KEY`.

## 📊 Rate Limiting

API requests are rate-limited to prevent abuse:

- **Window**: 15 minutes (configurable)
- **Limit**: 100 requests per window (configurable)
- **Headers**: Rate limit info included in response headers

When rate limit is exceeded, you'll receive a `429 Too Many Requests` response.

## 🌐 Base URL

All API endpoints are relative to your server's base URL:

```
http://localhost:3000/api
```

## 📋 Endpoints

### Health Check

Check if the system is running and healthy.

**Endpoint:** `GET /health`  
**Authentication:** None required  
**Rate Limited:** Yes

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "version": "1.0.0",
  "uptime": 3600
}
```

### System Status

Get comprehensive system status and statistics.

**Endpoint:** `GET /status`  
**Authentication:** Required  
**Rate Limited:** Yes

**Response:**
```json
{
  "scheduler": {
    "isRunning": true,
    "currentJob": {
      "id": 123,
      "url": "https://example.com/article",
      "status": "processing"
    },
    "stats": {
      "total": 150,
      "pending": 5,
      "processing": 1,
      "completed": 140,
      "failed": 4
    },
    "queueSize": 5,
    "processingInterval": "1 hours",
    "maxRetries": 3,
    "lastProcessedRow": "25"
  },
  "database": {
    "total": 150,
    "pending": 5,
    "processing": 1,
    "completed": 140,
    "failed": 4
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Add Job

Add a new URL to the processing queue.

**Endpoint:** `POST /jobs`  
**Authentication:** Required  
**Rate Limited:** Yes

**Request Body:**
```json
{
  "url": "https://example.com/article",
  "priority": 0
}
```

**Parameters:**
- `url` (required): Valid HTTP/HTTPS URL to process
- `priority` (optional): Priority level (higher numbers = higher priority)

**Response (Success):**
```json
{
  "success": true,
  "message": "URL added to processing queue",
  "jobId": 123,
  "url": "https://example.com/article"
}
```

**Response (Error - URL exists):**
```json
{
  "error": "Conflict",
  "message": "URL already exists in the system",
  "jobId": 45,
  "status": "completed"
}
```

### Get Job Details

Retrieve detailed information about a specific job.

**Endpoint:** `GET /jobs/:id`  
**Authentication:** Required  
**Rate Limited:** Yes

**Parameters:**
- `id`: Job ID
- `include_content` (query): Set to "true" to include processed content

**Response:**
```json
{
  "id": 123,
  "url": "https://example.com/article",
  "status": "completed",
  "title": "How to Land Your Dream Job",
  "slug": "how-to-land-your-dream-job",
  "wordpress_post_id": 456,
  "retry_count": 0,
  "error_message": null,
  "created_at": "2024-01-15T09:00:00.000Z",
  "updated_at": "2024-01-15T10:00:00.000Z",
  "processed_at": "2024-01-15T09:45:00.000Z",
  "published_at": "2024-01-15T10:00:00.000Z"
}
```

### List Jobs

Get a list of jobs with filtering and pagination.

**Endpoint:** `GET /jobs`  
**Authentication:** Required  
**Rate Limited:** Yes

**Query Parameters:**
- `status`: Filter by status (pending, processing, completed, failed, all)
- `limit`: Number of results per page (default: 20, max: 100)
- `offset`: Number of results to skip (default: 0)
- `sort`: Sort field (id, url, status, created_at, updated_at)
- `order`: Sort order (ASC, DESC)

**Example Request:**
```
GET /jobs?status=completed&limit=10&sort=updated_at&order=DESC
```

**Response:**
```json
{
  "jobs": [
    {
      "id": 123,
      "url": "https://example.com/article",
      "status": "completed",
      "title": "How to Land Your Dream Job",
      "wordpress_post_id": 456,
      "retry_count": 0,
      "created_at": "2024-01-15T09:00:00.000Z",
      "updated_at": "2024-01-15T10:00:00.000Z",
      "processed_at": "2024-01-15T09:45:00.000Z",
      "published_at": "2024-01-15T10:00:00.000Z"
    }
  ],
  "pagination": {
    "limit": 10,
    "offset": 0,
    "total": 150
  }
}
```

### Delete Job

Remove a job from the system.

**Endpoint:** `DELETE /jobs/:id`  
**Authentication:** Required  
**Rate Limited:** Yes

**Response:**
```json
{
  "success": true,
  "message": "Job deleted successfully"
}
```

### Get Job Logs

Retrieve processing logs for a specific job.

**Endpoint:** `GET /jobs/:id/logs`  
**Authentication:** Required  
**Rate Limited:** Yes

**Response:**
```json
{
  "jobId": 123,
  "logs": [
    {
      "id": 1,
      "job_id": 123,
      "step": "started",
      "status": "success",
      "message": "Job processing started",
      "details": null,
      "created_at": "2024-01-15T09:00:00.000Z"
    },
    {
      "id": 2,
      "job_id": 123,
      "step": "scraping",
      "status": "success",
      "message": "Web scraping completed",
      "details": "{\"title\":\"Article Title\",\"wordCount\":1200}",
      "created_at": "2024-01-15T09:15:00.000Z"
    }
  ]
}
```

### Manual Triggers

#### Trigger Processing

Manually start processing the next job in the queue.

**Endpoint:** `POST /trigger/process`  
**Authentication:** Required  
**Rate Limited:** Yes

**Response:**
```json
{
  "success": true,
  "message": "Processing triggered successfully"
}
```

#### Trigger URL Fetch

Manually fetch new URLs from Google Sheets.

**Endpoint:** `POST /trigger/fetch-urls`  
**Authentication:** Required  
**Rate Limited:** Yes

**Response:**
```json
{
  "success": true,
  "message": "URL fetch triggered successfully"
}
```

### Testing Endpoints

#### Test Scraping

Test scraping a specific URL without adding it to the queue.

**Endpoint:** `POST /test/scrape`  
**Authentication:** Required  
**Rate Limited:** Yes

**Request Body:**
```json
{
  "url": "https://example.com/test-article"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "title": "Test Article Title",
    "text": "Article content...",
    "wordCount": 500,
    "images": [],
    "scrapedAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "Scraping test successful"
}
```

#### Test Content Processing

Test Gemini AI content processing with sample content.

**Endpoint:** `POST /test/process`  
**Authentication:** Required  
**Rate Limited:** Yes

**Request Body:**
```json
{
  "title": "Sample Article Title",
  "content": "Sample article content to be processed..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "title": "SEO-Optimized Title",
    "content": "<h2>Processed content...</h2>",
    "excerpt": "Brief summary...",
    "metaDescription": "SEO meta description",
    "categories": ["Career Development"],
    "tags": ["jobs", "career"],
    "seoScore": 85
  },
  "message": "Content processing test successful"
}
```

#### Test WordPress Connection

Test connection to WordPress site.

**Endpoint:** `POST /test/wordpress`  
**Authentication:** Required  
**Rate Limited:** Yes

**Response:**
```json
{
  "success": true,
  "message": "WordPress connection successful",
  "data": {
    "categoriesCount": 15,
    "tagsCount": 50,
    "apiEndpoint": "https://your-site.com/wp-json/automated-blogging/v1"
  }
}
```

#### Test Google Sheets Connection

Test connection to Google Sheets.

**Endpoint:** `POST /test/sheets`  
**Authentication:** Required  
**Rate Limited:** Yes

**Response:**
```json
{
  "success": true,
  "message": "Google Sheets connection successful",
  "data": {
    "title": "Blog URLs Queue",
    "sheets": [
      {
        "title": "Sheet1",
        "sheetId": 0,
        "rowCount": 100,
        "columnCount": 4
      }
    ],
    "url": "https://docs.google.com/spreadsheets/d/SHEET_ID"
  }
}
```

## 🚨 Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error Type",
  "message": "Detailed error message"
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created (for POST requests)
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (missing or invalid API key)
- `404` - Not Found (resource doesn't exist)
- `409` - Conflict (resource already exists)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

## 📝 Usage Examples

### cURL Examples

**Add a job:**
```bash
curl -X POST http://localhost:3000/api/jobs \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"url": "https://example.com/article"}'
```

**Get job status:**
```bash
curl http://localhost:3000/api/jobs/123 \
  -H "X-API-Key: your-api-key"
```

**List completed jobs:**
```bash
curl "http://localhost:3000/api/jobs?status=completed&limit=5" \
  -H "X-API-Key: your-api-key"
```

### JavaScript Examples

**Using fetch API:**
```javascript
// Add a job
const response = await fetch('http://localhost:3000/api/jobs', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key'
  },
  body: JSON.stringify({
    url: 'https://example.com/article'
  })
});

const result = await response.json();
console.log(result);
```

**Using axios:**
```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  headers: {
    'X-API-Key': 'your-api-key'
  }
});

// Get system status
const status = await api.get('/status');
console.log(status.data);

// Add multiple jobs
const urls = [
  'https://example.com/article1',
  'https://example.com/article2'
];

for (const url of urls) {
  await api.post('/jobs', { url });
}
```

## 🔧 Integration Tips

### Webhook Integration

While the system doesn't provide webhooks out of the box, you can poll the API to monitor job status:

```javascript
async function monitorJob(jobId) {
  while (true) {
    const response = await api.get(`/jobs/${jobId}`);
    const job = response.data;
    
    if (job.status === 'completed') {
      console.log('Job completed!', job.wordpress_post_id);
      break;
    } else if (job.status === 'failed') {
      console.log('Job failed:', job.error_message);
      break;
    }
    
    // Wait 30 seconds before checking again
    await new Promise(resolve => setTimeout(resolve, 30000));
  }
}
```

### Batch Operations

For bulk operations, implement proper rate limiting:

```javascript
async function addUrlsBatch(urls) {
  for (let i = 0; i < urls.length; i++) {
    try {
      await api.post('/jobs', { url: urls[i] });
      console.log(`Added ${i + 1}/${urls.length}: ${urls[i]}`);
      
      // Rate limiting: wait 1 second between requests
      if (i < urls.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error(`Failed to add ${urls[i]}:`, error.response?.data?.message);
    }
  }
}
```

## 🔒 Security Best Practices

1. **API Key Security**: Never expose your API key in client-side code
2. **HTTPS**: Use HTTPS in production environments
3. **Rate Limiting**: Respect rate limits to avoid being blocked
4. **Input Validation**: Validate URLs before sending to the API
5. **Error Handling**: Implement proper error handling for all API calls

---

For more information, see the main README.md and SETUP.md files.
