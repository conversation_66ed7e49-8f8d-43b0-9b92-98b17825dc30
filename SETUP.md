# 🛠️ Detailed Setup Guide

This guide provides step-by-step instructions for setting up the Automated Blogging System.

## 📋 Prerequisites Checklist

Before starting, ensure you have:

- [ ] Node.js 18.0.0 or higher installed
- [ ] A Google Cloud Platform account
- [ ] A Gemini API key from Google AI Studio
- [ ] A WordPress website with admin access
- [ ] A Google Sheet with URLs to process

## 🔧 Step 1: System Installation

### 1.1 Clone the Repository

```bash
git clone <repository-url>
cd automated-blogging-system
```

### 1.2 Install Dependencies

```bash
npm install
```

### 1.3 Create Required Directories

```bash
mkdir -p config data logs temp
```

## 🔑 Step 2: Google Cloud Setup

### 2.1 Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "New Project"
3. Enter project name: "Automated Blogging System"
4. Click "Create"

### 2.2 Enable Google Sheets API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google Sheets API"
3. Click on it and press "Enable"

### 2.3 Create Service Account

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "Service Account"
3. Enter name: "blogging-system-service"
4. Click "Create and Continue"
5. Skip role assignment (click "Continue")
6. Click "Done"

### 2.4 Generate Service Account Key

1. Click on the created service account
2. Go to "Keys" tab
3. Click "Add Key" > "Create New Key"
4. Select "JSON" format
5. Click "Create"
6. Save the downloaded file as `config/google-service-account.json`

### 2.5 Note the Service Account Email

Copy the service account email (looks like: `<EMAIL>`)

## 📊 Step 3: Google Sheets Setup

### 3.1 Create Your Google Sheet

1. Go to [Google Sheets](https://sheets.google.com)
2. Create a new spreadsheet
3. Name it "Blog URLs Queue"

### 3.2 Set Up Sheet Structure

Create the following columns in row 1:

| A | B | C | D |
|---|---|---|---|
| URL | Status | Title | Notes |

### 3.3 Add Sample Data

```
Row 1: URL | Status | Title | Notes
Row 2: https://example.com/job-article-1 | | | 
Row 3: https://example.com/job-article-2 | | |
```

### 3.4 Share Sheet with Service Account

1. Click "Share" button in your Google Sheet
2. Add the service account email from step 2.5
3. Set permission to "Editor"
4. Uncheck "Notify people"
5. Click "Share"

### 3.5 Get Sheet ID

Copy the Sheet ID from the URL:
`https://docs.google.com/spreadsheets/d/SHEET_ID_HERE/edit`

## 🤖 Step 4: Gemini API Setup

### 4.1 Get Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Click "Create API Key"
3. Select your Google Cloud project
4. Click "Create API Key in existing project"
5. Copy the generated API key

## 🌐 Step 5: WordPress Setup

### 5.1 Upload Plugin

1. Download the plugin file: `wordpress-plugin/automated-blogging-api.php`
2. In WordPress Admin, go to Plugins > Add New
3. Click "Upload Plugin"
4. Choose the downloaded file
5. Click "Install Now"
6. Click "Activate Plugin"

### 5.2 Configure Plugin

1. Go to Settings > Blogging API
2. Copy the generated API Key
3. Optionally add your server's IP address to "Allowed IP Addresses"
4. Click "Save Changes"

### 5.3 Test Plugin Installation

Visit: `https://your-wordpress-site.com/wp-json/automated-blogging/v1/health`

You should see a JSON response with status "healthy".

## ⚙️ Step 6: Environment Configuration

### 6.1 Create Environment File

```bash
cp .env.example .env
```

### 6.2 Configure Environment Variables

Edit `.env` file with your values:

```env
# Google Sheets Configuration
GOOGLE_SHEETS_ID=your_sheet_id_from_step_3_5
GOOGLE_SERVICE_ACCOUNT_KEY_PATH=./config/google-service-account.json

# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_from_step_4_1

# WordPress Configuration
WORDPRESS_SITE_URL=https://your-wordpress-site.com
WORDPRESS_API_KEY=your_wordpress_api_key_from_step_5_2

# Database Configuration
DATABASE_PATH=./data/blogging_system.db

# Server Configuration
PORT=3000
NODE_ENV=development

# Security
API_SECRET_KEY=generate_a_random_32_character_string_here

# Optional: Scraping Configuration
SCRAPING_DELAY_MIN=5000
SCRAPING_DELAY_MAX=15000
MAX_RETRIES=3

# Optional: Scheduler Configuration
PROCESSING_INTERVAL_HOURS=1
MAX_CONCURRENT_JOBS=1
```

### 6.3 Generate API Secret Key

Generate a secure random string for API_SECRET_KEY:

```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

## 🧪 Step 7: Testing the Setup

### 7.1 Start the Application

```bash
npm run dev
```

You should see:
```
Automated Blogging System started on port 3000
Dashboard available at: http://localhost:3000/dashboard
```

### 7.2 Access the Dashboard

Open your browser and go to: `http://localhost:3000/dashboard`

### 7.3 Test Connections

In the dashboard:

1. Click "Test Connections"
2. Verify both Google Sheets and WordPress show "✅ Connected"

### 7.4 Test Manual URL Addition

1. In the dashboard, add a test URL in the "Add New URL" section
2. Click "Add URL"
3. Verify it appears in the "Recent Jobs" table

### 7.5 Test Manual Processing

1. Click "Trigger Processing" in the dashboard
2. Watch the job status change from "pending" to "processing" to "completed"
3. Check your WordPress site for the new post

## 🚀 Step 8: Production Deployment

### 8.1 Environment Configuration

Update `.env` for production:

```env
NODE_ENV=production
LOG_LEVEL=info
PORT=3000
```

### 8.2 Process Management

Install PM2 for process management:

```bash
npm install -g pm2
```

Create `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'automated-blogging-system',
    script: 'src/index.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

Start with PM2:

```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 8.3 Reverse Proxy (Optional)

If using Nginx, create a configuration:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔍 Step 9: Verification

### 9.1 Check System Status

Visit the dashboard and verify:
- [ ] All connection tests pass
- [ ] System statistics are displayed
- [ ] No error messages in the interface

### 9.2 Check Logs

```bash
tail -f logs/combined.log
```

Look for successful initialization messages.

### 9.3 Test End-to-End Flow

1. Add a URL to your Google Sheet
2. Wait for automatic processing (or trigger manually)
3. Verify the post appears on your WordPress site
4. Check that the content is properly formatted and SEO-optimized

## 🚨 Troubleshooting

### Common Setup Issues

**"Google Sheets access denied"**
- Verify service account has access to the sheet
- Check that the service account key file exists and is valid
- Ensure the Sheets API is enabled

**"WordPress API connection failed"**
- Verify the WordPress plugin is activated
- Check that the API key matches
- Ensure the WordPress site URL is correct and accessible

**"Gemini API error"**
- Verify the API key is correct
- Check that you have credits/quota available
- Ensure the Gemini API is enabled for your project

**"Database initialization failed"**
- Check that the data directory exists and is writable
- Verify Node.js has permission to create files in the project directory

### Getting Help

If you encounter issues:

1. Check the logs in `logs/error.log`
2. Verify all environment variables are set correctly
3. Test each component individually using the dashboard
4. Consult the main README.md for additional troubleshooting

## ✅ Setup Complete

Once all tests pass, your Automated Blogging System is ready for use! The system will:

- Check for new URLs in your Google Sheet every 30 minutes
- Process one URL every hour (configurable)
- Automatically publish SEO-optimized content to your WordPress site
- Provide real-time monitoring through the dashboard

Remember to monitor the system regularly and check the logs for any issues.
