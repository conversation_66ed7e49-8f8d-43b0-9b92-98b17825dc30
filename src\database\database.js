const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');

class Database {
  constructor() {
    this.db = null;
    this.dbPath = process.env.DATABASE_PATH || './data/blogging_system.db';
  }

  async initialize() {
    try {
      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // Connect to database
      this.db = new sqlite3.Database(this.dbPath);
      
      // Enable foreign keys
      await this.run('PRAGMA foreign_keys = ON');
      
      // Create tables
      await this.createTables();
      
      logger.info('Database initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database:', error);
      throw error;
    }
  }

  async createTables() {
    const tables = [
      // Jobs table for tracking URL processing
      `CREATE TABLE IF NOT EXISTS jobs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        url TEXT NOT NULL UNIQUE,
        status TEXT NOT NULL DEFAULT 'pending',
        title TEXT,
        content TEXT,
        processed_content TEXT,
        wordpress_post_id INTEGER,
        featured_image_url TEXT,
        categories TEXT,
        tags TEXT,
        slug TEXT,
        meta_description TEXT,
        error_message TEXT,
        retry_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        processed_at DATETIME,
        published_at DATETIME
      )`,
      
      // Processing queue table
      `CREATE TABLE IF NOT EXISTS processing_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        job_id INTEGER NOT NULL,
        priority INTEGER DEFAULT 0,
        scheduled_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        started_at DATETIME,
        completed_at DATETIME,
        FOREIGN KEY (job_id) REFERENCES jobs (id)
      )`,
      
      // System settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Processing logs table
      `CREATE TABLE IF NOT EXISTS processing_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        job_id INTEGER NOT NULL,
        step TEXT NOT NULL,
        status TEXT NOT NULL,
        message TEXT,
        details TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (job_id) REFERENCES jobs (id)
      )`
    ];

    for (const table of tables) {
      await this.run(table);
    }

    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs (status)',
      'CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs (created_at)',
      'CREATE INDEX IF NOT EXISTS idx_queue_scheduled_at ON processing_queue (scheduled_at)',
      'CREATE INDEX IF NOT EXISTS idx_logs_job_id ON processing_logs (job_id)'
    ];

    for (const index of indexes) {
      await this.run(index);
    }
  }

  // Promisify database operations
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Job management methods
  async createJob(url) {
    const result = await this.run(
      'INSERT INTO jobs (url) VALUES (?)',
      [url]
    );
    return result.id;
  }

  async getJob(id) {
    return await this.get('SELECT * FROM jobs WHERE id = ?', [id]);
  }

  async updateJob(id, updates) {
    const fields = Object.keys(updates);
    const values = Object.values(updates);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    await this.run(
      `UPDATE jobs SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    );
  }

  async getJobsByStatus(status) {
    return await this.all('SELECT * FROM jobs WHERE status = ? ORDER BY created_at ASC', [status]);
  }

  async getNextJobFromQueue() {
    return await this.get(`
      SELECT j.* FROM jobs j
      JOIN processing_queue pq ON j.id = pq.job_id
      WHERE j.status = 'pending' AND pq.started_at IS NULL
      ORDER BY pq.priority DESC, pq.scheduled_at ASC
      LIMIT 1
    `);
  }

  async addToQueue(jobId, priority = 0) {
    await this.run(
      'INSERT INTO processing_queue (job_id, priority) VALUES (?, ?)',
      [jobId, priority]
    );
  }

  async markQueueItemStarted(jobId) {
    await this.run(
      'UPDATE processing_queue SET started_at = CURRENT_TIMESTAMP WHERE job_id = ?',
      [jobId]
    );
  }

  async markQueueItemCompleted(jobId) {
    await this.run(
      'UPDATE processing_queue SET completed_at = CURRENT_TIMESTAMP WHERE job_id = ?',
      [jobId]
    );
  }

  // Logging methods
  async addProcessingLog(jobId, step, status, message = null, details = null) {
    await this.run(
      'INSERT INTO processing_logs (job_id, step, status, message, details) VALUES (?, ?, ?, ?, ?)',
      [jobId, step, status, message, details ? JSON.stringify(details) : null]
    );
  }

  async getProcessingLogs(jobId) {
    return await this.all(
      'SELECT * FROM processing_logs WHERE job_id = ? ORDER BY created_at ASC',
      [jobId]
    );
  }

  // Settings methods
  async getSetting(key) {
    const row = await this.get('SELECT value FROM settings WHERE key = ?', [key]);
    return row ? row.value : null;
  }

  async setSetting(key, value) {
    await this.run(
      'INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
      [key, value]
    );
  }

  // Statistics methods
  async getStats() {
    const [total, pending, processing, completed, failed] = await Promise.all([
      this.get('SELECT COUNT(*) as count FROM jobs'),
      this.get('SELECT COUNT(*) as count FROM jobs WHERE status = "pending"'),
      this.get('SELECT COUNT(*) as count FROM jobs WHERE status = "processing"'),
      this.get('SELECT COUNT(*) as count FROM jobs WHERE status = "completed"'),
      this.get('SELECT COUNT(*) as count FROM jobs WHERE status = "failed"')
    ]);

    return {
      total: total.count,
      pending: pending.count,
      processing: processing.count,
      completed: completed.count,
      failed: failed.count
    };
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

module.exports = new Database();
