const axios = require('axios');
const sharp = require('sharp');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

class WordPressPublisherService {
  constructor() {
    this.siteUrl = process.env.WORDPRESS_SITE_URL;
    this.apiKey = process.env.WORDPRESS_API_KEY;
    this.apiEndpoint = `${this.siteUrl}/wp-json/automated-blogging/v1`;
    this.timeout = 30000;
  }

  async initialize() {
    try {
      // Validate configuration
      if (!this.siteUrl || !this.apiKey) {
        throw new Error('WordPress site URL and API key are required');
      }

      // Test connection
      const isHealthy = await this.healthCheck();
      if (!isHealthy) {
        throw new Error('WordPress API health check failed');
      }

      logger.info('WordPress publisher initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize WordPress publisher:', error);
      throw error;
    }
  }

  async publishPost(processedContent, options = {}) {
    try {
      const {
        title,
        content,
        excerpt,
        slug,
        categories,
        tags,
        metaDescription,
        featuredImageSuggestions
      } = processedContent;

      // Prepare featured image
      let featuredImageUrl = null;
      if (options.generateFeaturedImage && featuredImageSuggestions?.length > 0) {
        featuredImageUrl = await this.generateFeaturedImage(title, featuredImageSuggestions[0]);
      }

      // Prepare post data
      const postData = {
        title: title,
        content: content,
        excerpt: excerpt,
        slug: slug,
        categories: categories || [],
        tags: tags || [],
        meta_description: metaDescription,
        status: options.status || 'publish',
        author_id: options.authorId || 1
      };

      // Add featured image if available
      if (featuredImageUrl) {
        postData.featured_image_url = featuredImageUrl;
      }

      // Make API request
      const response = await axios.post(`${this.apiEndpoint}/publish`, postData, {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        timeout: this.timeout
      });

      if (response.data.success) {
        logger.logPublishing(processedContent.originalUrl, 'success', {
          postId: response.data.post_id,
          postUrl: response.data.post_url,
          title: title
        });

        return {
          success: true,
          postId: response.data.post_id,
          postUrl: response.data.post_url,
          editUrl: response.data.edit_url,
          featuredImageId: response.data.featured_image_id,
          message: 'Post published successfully'
        };
      } else {
        throw new Error('WordPress API returned unsuccessful response');
      }

    } catch (error) {
      logger.logPublishing(processedContent.originalUrl, 'error', {
        error: error.message,
        title: processedContent.title
      });

      if (error.response) {
        // API error response
        throw new Error(`WordPress API error: ${error.response.status} - ${error.response.data?.message || error.message}`);
      } else if (error.request) {
        // Network error
        throw new Error(`WordPress API network error: ${error.message}`);
      } else {
        // Other error
        throw new Error(`WordPress publishing error: ${error.message}`);
      }
    }
  }

  async healthCheck() {
    try {
      const response = await axios.get(`${this.apiEndpoint}/health`, {
        timeout: 10000
      });

      return response.data.status === 'healthy';
    } catch (error) {
      logger.error('WordPress health check failed:', error.message);
      return false;
    }
  }

  async getCategories() {
    try {
      const response = await axios.get(`${this.apiEndpoint}/categories`, {
        headers: {
          'X-API-Key': this.apiKey
        },
        timeout: this.timeout
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to get WordPress categories:', error.message);
      return [];
    }
  }

  async getTags() {
    try {
      const response = await axios.get(`${this.apiEndpoint}/tags`, {
        headers: {
          'X-API-Key': this.apiKey
        },
        timeout: this.timeout
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to get WordPress tags:', error.message);
      return [];
    }
  }

  async generateFeaturedImage(title, imageDescription) {
    try {
      // For now, we'll create a simple text-based featured image
      // In a production environment, you might want to integrate with:
      // - Unsplash API for stock photos
      // - DALL-E or Midjourney for AI-generated images
      // - Canva API for designed graphics

      const imageBuffer = await this.createTextImage(title);
      
      // Save temporary image
      const tempDir = path.join(process.cwd(), 'temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const filename = `featured-${Date.now()}.jpg`;
      const filepath = path.join(tempDir, filename);
      
      await sharp(imageBuffer)
        .jpeg({ quality: 80 })
        .toFile(filepath);

      // In a real implementation, you would upload this to your CDN or media server
      // For now, we'll return a placeholder URL
      const imageUrl = `${this.siteUrl}/wp-content/uploads/automated-images/${filename}`;
      
      // Clean up temp file
      setTimeout(() => {
        if (fs.existsSync(filepath)) {
          fs.unlinkSync(filepath);
        }
      }, 5000);

      return imageUrl;

    } catch (error) {
      logger.error('Failed to generate featured image:', error.message);
      return null;
    }
  }

  async createTextImage(title) {
    try {
      const width = parseInt(process.env.FEATURED_IMAGE_WIDTH) || 1200;
      const height = parseInt(process.env.FEATURED_IMAGE_HEIGHT) || 630;

      // Create a simple gradient background with text overlay
      const svg = `
        <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
            </linearGradient>
          </defs>
          <rect width="100%" height="100%" fill="url(#grad1)" />
          <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="48" font-weight="bold" 
                fill="white" text-anchor="middle" dominant-baseline="middle">
            <tspan x="50%" dy="0">${this.wrapText(title, 20)}</tspan>
          </text>
        </svg>
      `;

      return Buffer.from(svg);
    } catch (error) {
      logger.error('Failed to create text image:', error.message);
      throw error;
    }
  }

  wrapText(text, maxLength) {
    if (text.length <= maxLength) {
      return text;
    }

    const words = text.split(' ');
    const lines = [];
    let currentLine = '';

    for (const word of words) {
      if ((currentLine + word).length <= maxLength) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) {
          lines.push(currentLine);
        }
        currentLine = word;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    // Return first two lines only for featured image
    return lines.slice(0, 2).join('\n');
  }

  async testConnection() {
    try {
      const health = await this.healthCheck();
      if (!health) {
        return {
          success: false,
          message: 'WordPress API health check failed'
        };
      }

      const categories = await this.getCategories();
      const tags = await this.getTags();

      return {
        success: true,
        message: 'WordPress connection successful',
        data: {
          categoriesCount: categories.length,
          tagsCount: tags.length,
          apiEndpoint: this.apiEndpoint
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `WordPress connection failed: ${error.message}`
      };
    }
  }

  // Method to publish a test post
  async publishTestPost() {
    try {
      const testContent = {
        title: 'Test Post from Automated Blogging System',
        content: '<p>This is a test post created by the automated blogging system to verify the WordPress integration is working correctly.</p><h2>Features Tested</h2><ul><li>Post creation</li><li>Category assignment</li><li>Tag assignment</li><li>Meta description</li></ul>',
        excerpt: 'This is a test post to verify WordPress integration.',
        slug: 'automated-blogging-test-post',
        categories: ['Test'],
        tags: ['automation', 'test', 'blogging'],
        metaDescription: 'Test post created by the automated blogging system to verify WordPress integration.',
        originalUrl: 'https://test.example.com'
      };

      const result = await this.publishPost(testContent, { status: 'draft' });
      return result;
    } catch (error) {
      throw new Error(`Test post publishing failed: ${error.message}`);
    }
  }
}

module.exports = new WordPressPublisherService();
